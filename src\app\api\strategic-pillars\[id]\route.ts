import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

interface RouteParams {
  params: Promise<{ id: string }>
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const payload = await getPayload({ config })
    const { id } = await params

    let result

    // First try to find by ID (if it's a valid ObjectId format)
    if (id.match(/^[0-9a-fA-F]{24}$/)) {
      try {
        result = await payload.findByID({
          collection: 'strategic-pillars',
          id,
          populate: {
            relatedProjects: true,
          },
        })
      } catch (error) {
        // If ID lookup fails, try pillarId lookup
        result = null
      }
    }

    // If no result from ID lookup or ID is not ObjectId format, try pillarId lookup
    if (!result) {
      const pillarIdResult = await payload.find({
        collection: 'strategic-pillars',
        where: {
          pillarId: {
            equals: id,
          },
        },
        limit: 1,
        populate: {
          relatedProjects: true,
        },
      })

      if (pillarIdResult.docs.length > 0) {
        result = pillarIdResult.docs[0]
      }
    }

    if (!result) {
      return NextResponse.json(
        {
          success: false,
          error: 'Strategic pillar not found',
        },
        { status: 404 },
      )
    }

    return NextResponse.json({
      success: true,
      pillar: result,
    })
  } catch (error) {
    console.error('Strategic Pillar by ID API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    )
  }
}
