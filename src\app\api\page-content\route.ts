import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const pageSlug = searchParams.get('pageSlug')
    const sectionKey = searchParams.get('sectionKey')
    const published = searchParams.get('published') !== 'false'

    // Build where clause
    const where: any = {}
    
    if (published) {
      where.published = { equals: true }
    }
    
    if (pageSlug) {
      where.pageSlug = { equals: pageSlug }
    }
    
    if (sectionKey) {
      where.sectionKey = { equals: sectionKey }
    }

    // Fetch page content from PayloadCMS
    const result = await payload.find({
      collection: 'page-content',
      where,
      page,
      limit,
      sort: ['displayOrder'],
      populate: {
        backgroundImage: true,
        featuredImage: true,
      },
    })

    return NextResponse.json({
      success: true,
      content: result.docs,
      pagination: {
        totalDocs: result.totalDocs,
        totalPages: result.totalPages,
        page: result.page,
        limit: result.limit,
        hasNextPage: result.hasNextPage,
        hasPrevPage: result.hasPrevPage,
      }
    })
  } catch (error) {
    console.error('Page Content API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    )
  }
}
