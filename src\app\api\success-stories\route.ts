import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const category = searchParams.get('category')
    const featured = searchParams.get('featured')
    const county = searchParams.get('county')
    const search = searchParams.get('search')

    // Build where clause
    const where: any = {
      published: { equals: true }
    }

    if (category) where.category = { equals: category }
    if (featured === 'true') where.featured = { equals: true }
    if (county) where['location.county'] = { equals: county }

    if (search) {
      where.or = [
        { title: { contains: search } },
        { summary: { contains: search } }
      ]
    }

    // Fetch success stories from PayloadCMS
    const result = await payload.find({
      collection: 'success-stories',
      where,
      page,
      limit,
      sort: featured === 'true' ? ['-featured', '-createdAt'] : ['-createdAt'],
      populate: {
        'image.image': true,
        'location.county': true,
      },
    })

    return NextResponse.json({
      success: true,
      stories: result.docs,
      pagination: {
        totalDocs: result.totalDocs,
        totalPages: result.totalPages,
        page: result.page,
        limit: result.limit,
        hasNextPage: result.hasNextPage,
        hasPrevPage: result.hasPrevPage,
      }
    })
  } catch (error) {
    console.error('Success Stories API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    )
  }
}
