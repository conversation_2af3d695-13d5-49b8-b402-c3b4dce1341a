'use client'

import React, { useState, useEffect } from 'react'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import { NPIPartners } from '@/components/ui/npi-partners'

interface NPIPartnersBlockProps {
  title?: string
  description?: string
  showNames?: boolean
}

export const NPIPartnersBlock: React.FC<NPIPartnersBlockProps> = ({
  title = 'Our Partners',
  description = "Working together with leading organizations to drive sustainable development and innovation in Kenya's natural products sector.",
  showNames = false,
}) => {
  const [partners, setPartners] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchPartners = async () => {
      try {
        setLoading(true)
        setError(null)

        const response = await fetch('/api/partners?limit=12')

        if (!response.ok) {
          throw new Error(`Failed to fetch partners: ${response.status}`)
        }

        const data = await response.json()

        if (data.success && data.partners) {
          setPartners(data.partners)
        } else {
          throw new Error('Invalid response format')
        }
      } catch (err) {
        console.error('Error fetching partners:', err)
        setError(err instanceof Error ? err.message : 'Failed to load partners')
        // Fallback to empty array
        setPartners([])
      } finally {
        setLoading(false)
      }
    }

    fetchPartners()
  }, [])

  // Helper function to get partner logo URL
  const getPartnerLogo = (partner: any): string => {
    if (partner.logo?.image?.url) {
      return partner.logo.image.url
    }
    // Fallback to placeholder
    return 'https://picsum.photos/200/100?random=1'
  }

  // Helper function to get partner URL
  const getPartnerUrl = (partner: any): string => {
    return partner.contact?.website || '#'
  }

  // Transform CMS data to component format
  const transformedPartners = partners.map((partner) => ({
    name: partner.name,
    logo: getPartnerLogo(partner),
    url: getPartnerUrl(partner),
    description: partner.description?.root?.children?.[0]?.children?.[0]?.text || partner.name,
  }))

  if (loading) {
    return (
      <NPISection className="bg-tertiary">
        <NPISectionHeader>
          <NPISectionTitle className="text-primary">{title}</NPISectionTitle>
          <NPISectionDescription className="font-light">{description}</NPISectionDescription>
        </NPISectionHeader>
        <div className="flex justify-center items-center py-12">
          <div className="text-primary">Loading partners...</div>
        </div>
      </NPISection>
    )
  }

  if (error) {
    return (
      <NPISection className="bg-tertiary">
        <NPISectionHeader>
          <NPISectionTitle className="text-primary">{title}</NPISectionTitle>
          <NPISectionDescription className="font-light">{description}</NPISectionDescription>
        </NPISectionHeader>
        <div className="flex justify-center items-center py-12">
          <div className="text-red-600">Error: {error}</div>
        </div>
      </NPISection>
    )
  }

  return (
    <NPISection className="bg-tertiary">
      <NPISectionHeader>
        <NPISectionTitle className="text-primary">{title}</NPISectionTitle>
        <NPISectionDescription className="font-light">{description}</NPISectionDescription>
      </NPISectionHeader>

      <NPIPartners partners={transformedPartners} variant="grid" showNames={showNames} />
    </NPISection>
  )
}
