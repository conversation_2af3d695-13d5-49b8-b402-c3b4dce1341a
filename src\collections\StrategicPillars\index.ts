import type { CollectionConfig } from 'payload'
import { authenticated } from '../../access/authenticated'
import { anyone } from '../../access/anyone'
import { generateUniversalId, getIdFieldConfig } from '../../hooks/generateUniversalId'
import { sanitizeForDatabase } from '../../hooks/sanitizeForDatabase'

export const StrategicPillars: CollectionConfig = {
  slug: 'strategic-pillars',
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'pillarId', 'order', 'published'],
    group: 'Content Management',
  },
  labels: {
    singular: 'Strategic Pillar',
    plural: 'Strategic Pillars',
  },
  fields: [
    getIdFieldConfig('strategic-pillars'),
    {
      name: 'pillarId',
      type: 'text',
      required: true,
      unique: true,
      admin: {
        description: 'Unique identifier for this pillar (e.g., knowledge-documentation)',
      },
    },
    {
      name: 'title',
      type: 'text',
      required: true,
      admin: {
        description: 'The pillar title (e.g., Indigenous Knowledge Documentation)',
      },
    },
    {
      name: 'shortTitle',
      type: 'text',
      admin: {
        description: 'Short version of title for cards and navigation',
      },
    },
    {
      name: 'description',
      type: 'richText',
      required: true,
      admin: {
        description: 'Detailed description of this strategic pillar',
      },
    },
    {
      name: 'summary',
      type: 'textarea',
      required: true,
      maxLength: 300,
      admin: {
        description: 'Brief summary for cards and previews (max 300 characters)',
      },
    },
    {
      name: 'icon',
      type: 'select',
      required: true,
      options: [
        { label: 'Database (Knowledge Documentation)', value: 'database' },
        { label: 'Lightbulb (Product Development)', value: 'lightbulb' },
        { label: 'Users (Capacity Building)', value: 'users' },
        { label: 'Shield (IP Protection)', value: 'shield' },
        { label: 'Globe (Market Development)', value: 'globe' },
        { label: 'Cog (Operations)', value: 'cog' },
      ],
      admin: {
        description: 'Icon to display for this pillar',
      },
    },
    {
      name: 'color',
      type: 'group',
      fields: [
        {
          name: 'primary',
          type: 'text',
          defaultValue: '#8A3E25',
          admin: {
            description: 'Primary color for this pillar (hex code)',
          },
        },
        {
          name: 'secondary',
          type: 'text',
          defaultValue: '#725242',
          admin: {
            description: 'Secondary color for gradients (hex code)',
          },
        },
      ],
    },
    {
      name: 'objectives',
      type: 'array',
      required: true,
      fields: [
        {
          name: 'objective',
          type: 'text',
          required: true,
        },
      ],
      admin: {
        description: 'Key objectives for this pillar',
      },
    },
    {
      name: 'keyMetrics',
      type: 'group',
      fields: [
        {
          name: 'targetValue',
          type: 'number',
          admin: {
            description: 'Target metric value',
          },
        },
        {
          name: 'currentValue',
          type: 'number',
          admin: {
            description: 'Current achieved value',
          },
        },
        {
          name: 'unit',
          type: 'text',
          admin: {
            description: 'Unit of measurement (e.g., counties, projects, people)',
          },
        },
        {
          name: 'description',
          type: 'text',
          admin: {
            description: 'Description of what this metric measures',
          },
        },
      ],
    },
    {
      name: 'milestones',
      type: 'array',
      fields: [
        {
          name: 'title',
          type: 'text',
          required: true,
        },
        {
          name: 'description',
          type: 'textarea',
        },
        {
          name: 'status',
          type: 'select',
          required: true,
          options: [
            { label: 'Completed', value: 'completed' },
            { label: 'In Progress', value: 'in-progress' },
            { label: 'Planned', value: 'planned' },
            { label: 'On Hold', value: 'on-hold' },
          ],
        },
        {
          name: 'targetDate',
          type: 'date',
        },
        {
          name: 'completedDate',
          type: 'date',
        },
      ],
      admin: {
        description: 'Key milestones for this pillar',
      },
    },
    {
      name: 'relatedProjects',
      type: 'relationship',
      relationTo: 'projects',
      hasMany: true,
      admin: {
        description: 'Projects that align with this pillar',
      },
    },
    {
      name: 'order',
      type: 'number',
      required: true,
      defaultValue: 1,
      admin: {
        description: 'Display order (1-4 for the four main pillars)',
      },
    },
    {
      name: 'published',
      type: 'checkbox',
      defaultValue: true,
      admin: {
        description: 'Make this pillar visible to the public',
      },
    },
  ],
  hooks: {
    beforeChange: [generateUniversalId, sanitizeForDatabase],
  },
}

export default StrategicPillars
