import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const category = searchParams.get('category')
    const featured = searchParams.get('featured')
    const published = searchParams.get('published') !== 'false'

    // Build where clause
    const where: any = {}
    
    if (published) {
      where.published = { equals: true }
    }
    
    if (category) {
      where.category = { equals: category }
    }
    
    if (featured === 'true') {
      where.featured = { equals: true }
    }

    // Fetch statistics from PayloadCMS
    const result = await payload.find({
      collection: 'statistics',
      where,
      page,
      limit,
      sort: ['displayOrder'],
    })

    return NextResponse.json({
      success: true,
      statistics: result.docs,
      pagination: {
        totalDocs: result.totalDocs,
        totalPages: result.totalPages,
        page: result.page,
        limit: result.limit,
        hasNextPage: result.hasNextPage,
        hasPrevPage: result.hasPrevPage,
      }
    })
  } catch (error) {
    console.error('Statistics API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    )
  }
}
