import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

export async function POST() {
  try {
    const payload = await getPayload({ config })
    
    // Try to create a simple county first
    const county = await payload.create({
      collection: 'counties',
      data: {
        name: 'Test County',
        code: 'TC-001',
        coordinates: { latitude: 0, longitude: 0 },
        description: 'Test county for seeding',
        isActive: true,
      },
    })
    
    console.log('Created county:', county)
    
    return NextResponse.json({
      success: true,
      message: 'Test county created successfully',
      county,
    })

  } catch (error) {
    console.error('Test create error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create test county',
        details: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    )
  }
}
