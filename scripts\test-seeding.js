/**
 * Simple script to test CMS seeding without starting the full dev server
 */

import { spawn } from 'child_process'

const BASE_URL = 'http://localhost:3000'

async function waitForServer(url, maxAttempts = 30, interval = 2000) {
  for (let i = 0; i < maxAttempts; i++) {
    try {
      const response = await fetch(url)
      if (response.ok) {
        console.log('✅ Server is ready!')
        return true
      }
    } catch (error) {
      // Server not ready yet
    }
    
    console.log(`⏳ Waiting for server... (attempt ${i + 1}/${maxAttempts})`)
    await new Promise(resolve => setTimeout(resolve, interval))
  }
  
  console.log('❌ Server failed to start within timeout')
  return false
}

async function testSeeding() {
  console.log('🚀 Starting development server...')
  
  // Start the dev server
  const serverProcess = spawn('npm', ['run', 'dev'], {
    stdio: 'pipe',
    shell: true,
    cwd: process.cwd()
  })

  let serverOutput = ''
  serverProcess.stdout.on('data', (data) => {
    const output = data.toString()
    serverOutput += output
    console.log(output)
  })

  serverProcess.stderr.on('data', (data) => {
    const output = data.toString()
    serverOutput += output
    console.error(output)
  })

  // Wait for server to be ready
  const serverReady = await waitForServer(`${BASE_URL}/api/health`)
  
  if (!serverReady) {
    console.log('❌ Server failed to start, terminating...')
    serverProcess.kill()
    process.exit(1)
  }

  try {
    console.log('🌱 Testing seeding endpoint...')
    
    const response = await fetch(`${BASE_URL}/api/seed`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    const result = await response.text()
    
    if (response.ok) {
      console.log('✅ Seeding completed successfully!')
      console.log('Response:', result)
    } else {
      console.log('❌ Seeding failed!')
      console.log('Status:', response.status)
      console.log('Response:', result)
    }

    // Test admin interface
    console.log('🔍 Testing admin interface...')
    const adminResponse = await fetch(`${BASE_URL}/admin`)
    
    if (adminResponse.ok) {
      console.log('✅ Admin interface is accessible!')
    } else {
      console.log('❌ Admin interface failed!')
      console.log('Status:', adminResponse.status)
    }

  } catch (error) {
    console.error('❌ Error during testing:', error.message)
  } finally {
    console.log('🛑 Terminating server...')
    serverProcess.kill()
    
    // Wait a bit for cleanup
    setTimeout(() => {
      process.exit(0)
    }, 2000)
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT, terminating...')
  process.exit(0)
})

process.on('SIGTERM', () => {
  console.log('\n🛑 Received SIGTERM, terminating...')
  process.exit(0)
})

testSeeding().catch(error => {
  console.error('❌ Test failed:', error)
  process.exit(1)
})
