import type { PayloadRequest } from 'payload'

export const debugMediaHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req

    // Get all media items
    const media = await payload.find({
      collection: 'media',
      limit: 10,
    })

    const mediaInfo = media.docs.map(doc => ({
      id: doc.id,
      filename: doc.filename,
      mimeType: doc.mimeType,
      storageType: doc.storageType,
      hasBase64Data: !!doc.base64Data,
      base64DataLength: doc.base64Data ? doc.base64Data.length : 0,
      url: doc.url,
      createdAt: doc.createdAt,
    }))

    return res.json({
      success: true,
      totalDocs: media.totalDocs,
      media: mediaInfo,
    })
  } catch (error) {
    console.error('Debug media error:', error)
    return res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

export const debugProjectsHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req

    // Get all projects with their image data
    const projects = await payload.find({
      collection: 'projects',
      limit: 5,
    })

    const projectInfo = projects.docs.map(doc => ({
      id: doc.id,
      title: doc.title,
      image: doc.image,
      slug: doc.slug,
    }))

    return res.json({
      success: true,
      totalDocs: projects.totalDocs,
      projects: projectInfo,
    })
  } catch (error) {
    console.error('Debug projects error:', error)
    return res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}
