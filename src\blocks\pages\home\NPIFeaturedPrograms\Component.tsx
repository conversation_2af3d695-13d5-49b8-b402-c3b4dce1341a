'use client'

import React, { useEffect, useState } from 'react'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import {
  NPICard,
  NPICardHeader,
  NPICardTitle,
  NPICardDescription,
  NPICardContent,
  NPICardFooter,
} from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import Image from 'next/image'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { Loader2 } from 'lucide-react'

interface CMSProject {
  id: string
  title: string
  summary: string
  image?: {
    image?: {
      url?: string
      alt?: string
    }
    alt?: string
    caption?: string
  }
  category: string
  location?: {
    specificLocation?: string
    counties?: Array<{ name: string }>
  }
  status: 'active' | 'completed' | 'upcoming' | 'planning' | 'on-hold' | 'cancelled'
  slug: string
}

interface NPIFeaturedProjectsProps {
  title?: string
  description?: string
  limit?: number
}

export const NPIFeaturedProjectsBlock: React.FC<NPIFeaturedProjectsProps> = ({
  title = 'Featured Projects',
  description = "Discover our key initiatives transforming Kenya's natural products landscape through community-driven innovation and sustainable development.",
  limit = 6,
}) => {
  const [projects, setProjects] = useState<CMSProject[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchFeaturedProjects = async () => {
      try {
        setLoading(true)
        setError(null)

        const response = await fetch(`/api/projects?featured=true&limit=${limit}`)

        if (!response.ok) {
          throw new Error(`Failed to fetch projects: ${response.status}`)
        }

        const data = await response.json()

        if (data.success && data.projects) {
          setProjects(data.projects)
        } else {
          throw new Error('Invalid response format')
        }
      } catch (err) {
        console.error('Error fetching featured projects:', err)
        setError(err instanceof Error ? err.message : 'Failed to load projects')
      } finally {
        setLoading(false)
      }
    }

    fetchFeaturedProjects()
  }, [limit])

  // Helper function to get image URL
  const getImageUrl = (project: CMSProject): string => {
    if (project.image?.image?.url) {
      return project.image.image.url
    }
    // Fallback to a default image
    return '/assets/product-placeholder.jpg'
  }

  // Helper function to get image alt text
  const getImageAlt = (project: CMSProject): string => {
    return project.image?.alt || project.image?.image?.alt || project.title
  }

  // Helper function to format location
  const getLocation = (project: CMSProject): string => {
    if (project.location?.specificLocation) {
      return project.location.specificLocation
    }
    if (project.location?.counties && project.location.counties.length > 0) {
      return project.location.counties.map(county => county.name).join(', ')
    }
    return 'Kenya'
  }

  // Helper function to format category
  const formatCategory = (category: string): string => {
    return category.split('-').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')
  }

  const getStatusColor = (status: CMSProject['status']) => {
    switch (status) {
      case 'active':
        return 'bg-[#25718A] text-white shadow-lg'
      case 'completed':
        return 'bg-[#725242] text-white shadow-lg'
      case 'upcoming':
      case 'planning':
        return 'bg-[#8A3E25] text-white shadow-lg'
      case 'on-hold':
        return 'bg-gray-500 text-white shadow-lg'
      case 'cancelled':
        return 'bg-red-500 text-white shadow-lg'
      default:
        return 'bg-black text-white shadow-lg'
    }
  }

  const getStatusLabel = (status: CMSProject['status']) => {
    switch (status) {
      case 'active':
        return 'Active'
      case 'completed':
        return 'Completed'
      case 'upcoming':
        return 'Upcoming'
      case 'planning':
        return 'Planning'
      case 'on-hold':
        return 'On Hold'
      case 'cancelled':
        return 'Cancelled'
      default:
        return status
    }
  }

  return (
    <NPISection size="sm" className="bg-white relative overflow-hidden">
      {/* Simple Background Elements */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Subtle pattern */}
        <div
          className="absolute inset-0 opacity-[0.02]"
          style={{
            backgroundImage: `
            linear-gradient(90deg, #725242 1px, transparent 1px),
            linear-gradient(#725242 1px, transparent 1px)
          `,
            backgroundSize: '60px 60px',
          }}
        />
      </div>

      <div className="relative z-10">
        <NPISectionHeader className="text-center mb-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="inline-flex items-center px-4 py-2 bg-[#8A3E25]/15 border border-[#8A3E25]/30 text-[#8A3E25] text-sm font-medium mb-3"
          >
            <span className="w-2 h-2 bg-[#8A3E25] mr-3"></span>
            Featured Projects
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <NPISectionTitle className="leading-[1.1] tracking-[-0.02em] mb-2 text-black font-bold text-2xl lg:text-3xl">
              {title}
            </NPISectionTitle>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <NPISectionDescription className="font-light leading-[1.6] text-black max-w-xl mx-auto text-sm lg:text-base">
              {description}
            </NPISectionDescription>
          </motion.div>
        </NPISectionHeader>

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-[#25718A]" />
            <span className="ml-2 text-[#725242]">Loading featured projects...</span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="text-center py-12">
            <p className="text-red-600 mb-4">Failed to load projects: {error}</p>
            <NPIButton
              onClick={() => window.location.reload()}
              variant="outline"
              className="border-[#25718A] text-[#25718A] hover:bg-[#25718A] hover:text-white"
            >
              Try Again
            </NPIButton>
          </div>
        )}

        {/* Projects Grid */}
        {!loading && !error && projects.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {projects.slice(0, 4).map((project, index) => (
            <motion.div
              key={project.id}
              initial={{ opacity: 0, y: 30, scale: 0.95 }}
              whileInView={{ opacity: 1, y: 0, scale: 1 }}
              viewport={{ once: true }}
              transition={{
                duration: 0.7,
                delay: index * 0.15,
                type: 'spring',
                stiffness: 100,
              }}
              whileHover={{ y: -6 }}
            >
              <NPICard
                className={`overflow-hidden shadow-lg border-2 hover:shadow-xl group transition-all duration-300 flex flex-col aspect-square w-full hover:scale-[1.05] hover:-translate-y-2 ${
                  index % 4 === 0
                    ? 'bg-[#725242] border-[#725242] hover:border-[#8A3E25] hover:shadow-[#725242]/30'
                    : index % 4 === 1
                      ? 'bg-[#EFE3BA] border-[#EFE3BA] hover:border-[#725242] hover:shadow-[#EFE3BA]/30'
                      : index % 4 === 2
                        ? 'bg-[#8A3E25] border-[#8A3E25] hover:border-[#725242] hover:shadow-[#8A3E25]/30'
                        : 'bg-white border-white hover:border-[#8A3E25] hover:shadow-white/30'
                }`}
              >
                {/* Square Image Section - Takes up top half */}
                <div className="relative h-1/2 w-full flex-shrink-0 overflow-hidden">
                  <Image
                    src={getImageUrl(project)}
                    alt={getImageAlt(project)}
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                  />
                  <div
                    className={`absolute inset-0 ${
                      index % 4 === 0
                        ? 'bg-black/40'
                        : index % 4 === 1
                          ? 'bg-[#725242]/40'
                          : index % 4 === 2
                            ? 'bg-black/40'
                            : 'bg-[#8A3E25]/40'
                    }`}
                  />
                  <div className="absolute top-2 left-2">
                    <span
                      className={`px-2 py-1 text-xs font-bold shadow-lg ${getStatusColor(project.status)}`}
                    >
                      {getStatusLabel(project.status)}
                    </span>
                  </div>
                  {/* Decorative corner accent */}
                  <div className="absolute top-0 right-0 w-6 h-6 bg-[#25718A]/50"></div>
                </div>

                {/* Content Section - Takes up bottom half */}
                <NPICardHeader
                  className={`h-1/2 p-3 flex-shrink-0 flex flex-col justify-between ${
                    index % 4 === 0
                      ? 'bg-white'
                      : index % 4 === 1
                        ? 'bg-white'
                        : index % 4 === 2
                          ? 'bg-white'
                          : 'bg-white'
                  }`}
                >
                  <div
                    className={`text-xs font-bold uppercase tracking-wide mb-1 ${
                      index % 4 === 0
                        ? 'text-[#725242]'
                        : index % 4 === 1
                          ? 'text-[#725242]'
                          : index % 4 === 2
                            ? 'text-[#725242]'
                            : 'text-[#725242]'
                    }`}
                  >
                    {formatCategory(project.category)}
                  </div>
                  <NPICardTitle
                    className={`text-sm font-bold leading-tight transition-colors line-clamp-2 flex-1 flex items-start ${
                      index % 4 === 0
                        ? 'text-black group-hover:text-[#8A3E25]'
                        : index % 4 === 1
                          ? 'text-black group-hover:text-[#8A3E25]'
                          : index % 4 === 2
                            ? 'text-black group-hover:text-[#8A3E25]'
                            : 'text-black group-hover:text-[#8A3E25]'
                    }`}
                  >
                    {project.title}
                  </NPICardTitle>
                  <div className="flex items-center justify-between mt-2">
                    <span className="text-xs text-gray-600">
                      {getLocation(project)}
                    </span>
                    <Link href={`/projects/${project.slug}`}>
                      <NPIButton
                        size="sm"
                        className="text-xs px-2 py-1 bg-[#25718A] hover:bg-[#8A3E25] text-white"
                      >
                        View
                      </NPIButton>
                    </Link>
                  </div>
                </NPICardHeader>
              </NPICard>
            </motion.div>
          ))}
        </div>
        )}

        {/* Empty State */}
        {!loading && !error && projects.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-600 mb-4">No featured projects available at the moment.</p>
            <Link href="/projects">
              <NPIButton
                variant="outline"
                className="border-[#25718A] text-[#25718A] hover:bg-[#25718A] hover:text-white"
              >
                View All Projects
              </NPIButton>
            </Link>
          </div>
        )}

        {/* View All Projects Button */}
        {!loading && !error && (
          <motion.div
            className="text-center mt-6"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.8 }}
          >
            <NPIButton
              asChild
              className="bg-[#8A3E25] hover:bg-[#25718A] text-white font-bold px-8 py-3 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 border-2 border-[#8A3E25] hover:border-[#25718A]"
            >
              <Link href="/projects" className="flex items-center gap-2">
                View All Projects
                <motion.span
                  animate={{ x: [0, 5, 0] }}
                  transition={{ duration: 1.5, repeat: Infinity, ease: 'easeInOut' }}
                >
                  →
                </motion.span>
              </Link>
            </NPIButton>
          </motion.div>
        )}
      </div>
    </NPISection>
  )
}
