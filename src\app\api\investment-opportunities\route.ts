import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams
    const limit = parseInt(searchParams.get('limit') || '10')
    const page = parseInt(searchParams.get('page') || '1')
    const sort = searchParams.get('sort') || '-publishDate'
    const featured = searchParams.get('featured')
    const sector = searchParams.get('sector')
    const investmentType = searchParams.get('investmentType')
    const status = searchParams.get('status')
    const urgent = searchParams.get('urgent')

    // Build where clause
    const where: any = {}

    if (featured !== null) {
      where.featured = { equals: featured === 'true' }
    }

    if (sector) {
      where.sector = { equals: sector }
    }

    if (investmentType) {
      where.investmentType = { equals: investmentType }
    }

    if (status) {
      where.status = { equals: status }
    } else {
      // Default to active opportunities only
      where.status = { equals: 'active' }
    }

    if (urgent !== null) {
      where.urgent = { equals: urgent === 'true' }
    }

    // Fetch investment opportunities from CMS
    const result = await payload.find({
      collection: 'investment-opportunities',
      where,
      limit,
      page,
      sort: [sort],
      select: {
        id: true,
        title: true,
        summary: true,
        description: true,
        sector: true,
        investmentType: true,
        status: true,
        featured: true,
        urgent: true,
        funding: true,
        timeline: true,
        location: true,
        publishDate: true,
        createdAt: true,
        updatedAt: true,
      },
    })

    return NextResponse.json({
      success: true,
      opportunities: result.docs,
      pagination: {
        totalDocs: result.totalDocs,
        limit: result.limit,
        totalPages: result.totalPages,
        page: result.page,
        pagingCounter: result.pagingCounter,
        hasPrevPage: result.hasPrevPage,
        hasNextPage: result.hasNextPage,
        prevPage: result.prevPage,
        nextPage: result.nextPage,
      },
    })
  } catch (error) {
    console.error('Investment Opportunities API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Failed to fetch investment opportunities',
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const body = await request.json()

    // Create new investment opportunity
    const result = await payload.create({
      collection: 'investment-opportunities',
      data: body,
    })

    return NextResponse.json({
      success: true,
      opportunity: result,
      message: 'Investment opportunity created successfully',
    })
  } catch (error) {
    console.error('Investment Opportunities POST API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Failed to create investment opportunity',
      },
      { status: 500 }
    )
  }
}
