import type { PayloadRequest } from 'payload'

interface TransformedPartnership {
  id: string
  title: string
  description: string
  summary: string
  type: string
  status: string
  partner?: any
  image?: TransformedMedia
  timeline: {
    startDate: string
    endDate?: string
    duration?: string
    renewalDate?: string
  }
  scope?: {
    objectives?: string[]
    activities?: Array<{
      activity: string
      description?: string
      status?: string
    }>
    deliverables?: Array<{
      deliverable: string
      dueDate?: string
      completed: boolean
    }>
  }
  resources?: {
    npiContribution?: {
      financial?: number
      inKind?: Array<{
        resource: string
        value?: number
      }>
      personnel?: Array<{
        name: string
        role: string
        timeCommitment?: string
      }>
    }
    partnerContribution?: {
      financial?: number
      inKind?: Array<{
        resource: string
        value?: number
      }>
      expertise?: Array<{
        area: string
        description?: string
      }>
    }
  }
  impact?: {
    beneficiaries?: number
    communities?: number
    counties?: TransformedCounty[]
    metrics?: Array<{
      metric: string
      target?: string
      achieved?: string
      unit?: string
    }>
    outcomes?: Array<{
      outcome: string
      description?: string
      evidence?: TransformedMedia
    }>
  }
  governance?: {
    agreementType?: string
    agreementDocument?: TransformedMedia
    keyContacts?: Array<{
      name: string
      role: string
      organization: string
      email?: string
      phone?: string
    }>
    reportingSchedule?: string
  }
  relatedProjects?: any[]
  documents?: Array<{
    title: string
    file: TransformedMedia
    type?: string
    confidential: boolean
  }>
  featured: boolean
  published: boolean
  tags?: string[]
  slug: string
  createdAt: string
  updatedAt: string
}

interface TransformedMedia {
  id: string
  filename: string
  url: string
  alt?: string
  width?: number
  height?: number
}

interface TransformedCounty {
  id: string
  name: string
  code?: string
}

interface PartnershipsResponse {
  partnerships: TransformedPartnership[]
  totalPartnerships: number
  page: number
  limit: number
  totalPages: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

// Utility functions
const extractTextFromLexical = (richTextData: any): string => {
  if (!richTextData || typeof richTextData === 'string') {
    return richTextData || ''
  }

  if (richTextData.root && richTextData.root.children) {
    return extractTextFromChildren(richTextData.root.children)
  }

  return ''
}

const extractTextFromChildren = (children: any[]): string => {
  if (!Array.isArray(children)) return ''

  return children
    .map((child) => {
      if (child.type === 'text') {
        return child.text || ''
      }
      if (child.children && Array.isArray(child.children)) {
        return extractTextFromChildren(child.children)
      }
      return ''
    })
    .join(' ')
    .replace(/\s+/g, ' ')
    .trim()
}

const transformMedia = (media: any): TransformedMedia | undefined => {
  if (!media || typeof media === 'string') return undefined

  return {
    id: media.id,
    filename: media.filename,
    url: media.url || `/api/media/file/${media.filename}`,
    alt: media.alt,
    width: media.width,
    height: media.height,
  }
}

const transformCounty = (county: any): TransformedCounty | undefined => {
  if (!county || typeof county === 'string') return undefined

  return {
    id: county.id,
    name: county.name,
    code: county.code,
  }
}

const transformPartnership = (partnership: any): TransformedPartnership => {
  return {
    id: partnership.id,
    title: partnership.title,
    description: extractTextFromLexical(partnership.description),
    summary: partnership.summary,
    type: partnership.type,
    status: partnership.status,
    partner: partnership.partner,
    image: transformMedia(partnership.image),
    timeline: {
      startDate: partnership.timeline?.startDate,
      endDate: partnership.timeline?.endDate,
      duration: partnership.timeline?.duration,
      renewalDate: partnership.timeline?.renewalDate,
    },
    scope: partnership.scope ? {
      objectives: Array.isArray(partnership.scope.objectives) 
        ? partnership.scope.objectives.map((obj: any) => obj.objective).filter(Boolean)
        : [],
      activities: Array.isArray(partnership.scope.activities) 
        ? partnership.scope.activities.map((activity: any) => ({
            activity: activity.activity,
            description: activity.description,
            status: activity.status,
          }))
        : [],
      deliverables: Array.isArray(partnership.scope.deliverables) 
        ? partnership.scope.deliverables.map((deliverable: any) => ({
            deliverable: deliverable.deliverable,
            dueDate: deliverable.dueDate,
            completed: deliverable.completed || false,
          }))
        : [],
    } : undefined,
    resources: partnership.resources ? {
      npiContribution: partnership.resources.npiContribution ? {
        financial: partnership.resources.npiContribution.financial,
        inKind: Array.isArray(partnership.resources.npiContribution.inKind) 
          ? partnership.resources.npiContribution.inKind.map((item: any) => ({
              resource: item.resource,
              value: item.value,
            }))
          : [],
        personnel: Array.isArray(partnership.resources.npiContribution.personnel) 
          ? partnership.resources.npiContribution.personnel.map((person: any) => ({
              name: person.name,
              role: person.role,
              timeCommitment: person.timeCommitment,
            }))
          : [],
      } : undefined,
      partnerContribution: partnership.resources.partnerContribution ? {
        financial: partnership.resources.partnerContribution.financial,
        inKind: Array.isArray(partnership.resources.partnerContribution.inKind) 
          ? partnership.resources.partnerContribution.inKind.map((item: any) => ({
              resource: item.resource,
              value: item.value,
            }))
          : [],
        expertise: Array.isArray(partnership.resources.partnerContribution.expertise) 
          ? partnership.resources.partnerContribution.expertise.map((exp: any) => ({
              area: exp.area,
              description: exp.description,
            }))
          : [],
      } : undefined,
    } : undefined,
    impact: partnership.impact ? {
      beneficiaries: partnership.impact.beneficiaries,
      communities: partnership.impact.communities,
      counties: Array.isArray(partnership.impact.counties) 
        ? partnership.impact.counties.map(transformCounty).filter(Boolean)
        : [],
      metrics: Array.isArray(partnership.impact.metrics) 
        ? partnership.impact.metrics.map((metric: any) => ({
            metric: metric.metric,
            target: metric.target,
            achieved: metric.achieved,
            unit: metric.unit,
          }))
        : [],
      outcomes: Array.isArray(partnership.impact.outcomes) 
        ? partnership.impact.outcomes.map((outcome: any) => ({
            outcome: outcome.outcome,
            description: outcome.description,
            evidence: transformMedia(outcome.evidence),
          }))
        : [],
    } : undefined,
    governance: partnership.governance ? {
      agreementType: partnership.governance.agreementType,
      agreementDocument: transformMedia(partnership.governance.agreementDocument),
      keyContacts: Array.isArray(partnership.governance.keyContacts) 
        ? partnership.governance.keyContacts.map((contact: any) => ({
            name: contact.name,
            role: contact.role,
            organization: contact.organization,
            email: contact.email,
            phone: contact.phone,
          }))
        : [],
      reportingSchedule: partnership.governance.reportingSchedule,
    } : undefined,
    relatedProjects: partnership.relatedProjects || [],
    documents: Array.isArray(partnership.documents) 
      ? partnership.documents.map((doc: any) => ({
          title: doc.title,
          file: transformMedia(doc.file),
          type: doc.type,
          confidential: doc.confidential || false,
        })).filter((doc: any) => doc.file)
      : [],
    featured: partnership.featured || false,
    published: partnership.published !== false,
    tags: Array.isArray(partnership.tags) 
      ? partnership.tags.map((tag: any) => tag.tag).filter(Boolean)
      : [],
    slug: partnership.slug,
    createdAt: partnership.createdAt,
    updatedAt: partnership.updatedAt,
  }
}

// Main Partnerships Handler
export const partnershipsHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req

    // Parse query parameters
    const {
      type,
      status,
      partner,
      featured,
      county,
      limit = '20',
      page = '1',
      sort = '-updatedAt',
      search,
    } = req.query as Record<string, string>

    // Safely parse integers with fallbacks
    const parsedPage = parseInt(page) || 1
    const parsedLimit = Math.min(parseInt(limit) || 20, 100) // Cap at 100

    // Build where clause
    const where: any = {
      published: { equals: true },
    }

    if (type) where.type = { equals: type }
    if (status) where.status = { equals: status }
    if (partner) where.partner = { equals: partner }
    if (featured === 'true') where.featured = { equals: true }
    if (county) where['impact.counties'] = { in: [county] }
    if (search) {
      where.or = [
        { title: { contains: search } },
        { summary: { contains: search } },
        { 'tags.tag': { contains: search } },
      ]
    }

    // Fetch partnerships with populated relationships
    const partnershipsResult = await payload.find({
      collection: 'partnerships',
      where,
      limit: parsedLimit,
      page: parsedPage,
      sort: sort as any,
      depth: 2, // Populate partners, counties, projects, etc.
    })

    // Transform partnerships
    const transformedPartnerships: TransformedPartnership[] = partnershipsResult.docs.map(transformPartnership)

    const currentPage = parsedPage
    const currentLimit = parsedLimit
    const totalPages = Math.ceil(partnershipsResult.totalDocs / currentLimit)

    const response = {
      success: true,
      partnerships: transformedPartnerships,
      totalPartnerships: partnershipsResult.totalDocs,
      page: currentPage,
      limit: currentLimit,
      totalPages,
      hasNextPage: currentPage < totalPages,
      hasPrevPage: currentPage > 1,
    }

    res.status(200).json(response)
  } catch (error) {
    console.error('Error in partnerships endpoint:', error)
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

// Get single partnership by ID or slug
export const partnershipByIdHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req
    const { id } = req.params

    // Try to find by ID first, then by slug
    let partnership
    try {
      partnership = await payload.findByID({
        collection: 'partnerships',
        id,
        depth: 2,
      })
    } catch {
      // If ID lookup fails, try slug
      const result = await payload.find({
        collection: 'partnerships',
        where: { slug: { equals: id } },
        limit: 1,
        depth: 2,
      })
      partnership = result.docs[0]
    }

    if (!partnership) {
      return res.status(404).json({
        success: false,
        error: 'Partnership not found',
        message: `No partnership found with ID or slug: ${id}`,
      })
    }

    // Check if published (unless user is authenticated)
    if (!partnership.published && !req.user) {
      return res.status(404).json({
        success: false,
        error: 'Partnership not found',
        message: 'Partnership is not published',
      })
    }

    const transformedPartnership = transformPartnership(partnership)

    res.status(200).json({
      success: true,
      partnership: transformedPartnership,
    })
  } catch (error) {
    console.error('Error in partnership by ID endpoint:', error)
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}
