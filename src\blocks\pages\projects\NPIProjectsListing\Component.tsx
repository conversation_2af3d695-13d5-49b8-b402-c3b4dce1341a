'use client'

import React, { useState } from 'react'
import {
  <PERSON>PI<PERSON><PERSON><PERSON>,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import { NPICard } from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import Image from 'next/image'
import { motion } from 'framer-motion'
import { MapPin, Loader2 } from 'lucide-react'
import { useProjects } from '@/lib/cms/hooks'
import type { CMSProject } from '@/lib/cms/types'
import { useRouter } from 'next/navigation'

// Helper function to validate and process image URL
const processImageUrl = (image: any): string => {
  // Handle null or undefined
  if (!image) {
    return '/assets/product 1.jpg'
  }

  // Handle Media object stored in database
  if (typeof image === 'object') {
    // Check if it's a database-stored media object
    if (image.id) {
      // If storageType is database or if URL starts with /api/media/database, use database endpoint
      if (image.storageType === 'database' || (image.url && image.url.includes('/api/media/database/'))) {
        return `/api/media/database/${image.id}`
      }
      // For any media object with an ID, try the database endpoint first
      // This handles cases where storageType might not be populated in relationships
      return `/api/media/database/${image.id}`
    }

    // Handle enhanced image field (group with image property)
    if (image.image) {
      if (typeof image.image === 'object') {
        if (image.image.id) {
          // If storageType is database or if URL starts with /api/media/database, use database endpoint
          if (image.image.storageType === 'database' || (image.image.url && image.image.url.includes('/api/media/database/'))) {
            return `/api/media/database/${image.image.id}`
          }
          // For any media object with an ID, try the database endpoint first
          return `/api/media/database/${image.image.id}`
        }
        if (image.image.url) {
          return image.image.url
        }
      }
      if (typeof image.image === 'string') {
        return image.image
      }
    }

    // Handle direct URL property
    if (image.url) {
      return image.url
    }
  }

  // Handle string URL (legacy format)
  if (typeof image === 'string' && image.trim()) {
    // If it's a base64 data URL, return as is
    if (image.startsWith('data:image/')) {
      return image
    }

    // If it's a regular URL, validate it
    try {
      new URL(image)
      return image
    } catch {
      // If URL is invalid, return fallback
      return '/assets/product 1.jpg'
    }
  }

  // Fallback for undefined or invalid image
  return '/assets/product 1.jpg'
}

// Transform CMS project to display format
const transformProject = (cmsProject: CMSProject): Project => {
  return {
    id: cmsProject.slug || cmsProject.id, // Use slug for URL-friendly routing
    title: cmsProject.title,
    description: cmsProject.summary || '', // Use summary for card display
    category: cmsProject.category,
    pillar: cmsProject.pillar,
    location:
      cmsProject.location?.specificLocation ||
      (cmsProject.location?.counties && cmsProject.location.counties.length > 0
        ? `${cmsProject.location.counties.length} Counties`
        : 'Kenya'),
    duration:
      cmsProject.timeline?.duration ||
      (cmsProject.timeline?.startDate
        ? `Started ${new Date(cmsProject.timeline.startDate).getFullYear()}`
        : 'Ongoing'),
    status: cmsProject.status as 'active' | 'completed' | 'planning' | 'on-hold' | 'cancelled',
    participants: cmsProject.impact?.beneficiaries || 0,
    budget: cmsProject.budget?.totalBudget
      ? `${cmsProject.budget.currency || 'KES'} ${cmsProject.budget.totalBudget.toLocaleString()}`
      : 'Budget TBD',
    objectives: [], // Will be populated from full description if needed
    partners:
      (cmsProject as any).team?.implementingPartners?.map((p: any) =>
        typeof p.partner === 'string' ? p.partner : p.partner?.name || 'Partner',
      ) || [],
    image: processImageUrl(cmsProject.image), // Process and validate image URL
    imageMetadata: cmsProject.image && typeof cmsProject.image === 'object' ? {
      alt: cmsProject.image.alt,
      width: cmsProject.image.width,
      height: cmsProject.image.height
    } : undefined,
  }
}

interface Project { 
  id: string
  title: string
  description: string
  category: string
  pillar: string
  location: string
  duration: string
  status: 'active' | 'completed' | 'planning' | 'on-hold' | 'cancelled'
  participants: number
  budget: string
  objectives: string[]
  partners: string[]
  image: string
  imageMetadata?: {
    filename?: string
    alt?: string
    width?: number
    height?: number
  }
}

interface NPIProjectsListingProps {
  title?: string
  description?: string
  projects?: Project[]
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'active':
      return 'bg-[#25718A] text-white'
    case 'completed':
      return 'bg-[#8A3E25] text-white'
    case 'planning':
      return 'bg-[#725242] text-white'
    case 'on-hold':
      return 'bg-yellow-600 text-white'
    case 'cancelled':
      return 'bg-red-600 text-white'
    default:
      return 'bg-[#725242] text-white'
  }
}

const getCategoryColor = (category: string) => {
  const colors = [
    { bg: 'bg-[#8A3E25]', text: 'text-white', border: 'border-[#8A3E25]' },
    { bg: 'bg-[#25718A]', text: 'text-white', border: 'border-[#25718A]' },
    { bg: 'bg-[#725242]', text: 'text-white', border: 'border-[#725242]' },
    { bg: 'bg-[#EFE3BA]', text: 'text-black', border: 'border-[#8A3E25]' },
    { bg: 'bg-[#FFFFFF]', text: 'text-black', border: 'border-[#25718A]' },
  ]
  const index = category.length % colors.length
  return colors[index]
}

export const NPIProjectsListingBlock: React.FC<NPIProjectsListingProps> = ({
  title = 'Our Projects & Initiatives',
  description = "Comprehensive projects transforming Kenya's natural products landscape through community-driven innovation, capacity building, and sustainable development.",
  projects: propProjects,
}) => {
  const [filter, setFilter] = useState<string>('all')
  const router = useRouter()

  // Fetch projects from CMS
  const {
    data: cmsProjects,
    loading,
    error,
  } = useProjects({
    published: true,
    limit: 50, // Get more projects for better filtering
  })

  // Use prop projects as fallback, or transform CMS projects
  const projects = propProjects || (cmsProjects ? cmsProjects.map(transformProject) : [])

  const categories = ['all', ...Array.from(new Set(projects.map((p) => p.category)))]
  const filteredProjects =
    filter === 'all' ? projects : projects.filter((p) => p.category === filter)

  // Loading state
  if (loading) {
    return (
      <NPISection className="py-24 bg-[#FFFFFF]">
        <div className="container mx-auto px-4">
          <NPISectionHeader className="text-center mb-16">
            <NPISectionTitle className="text-black mb-4">{title}</NPISectionTitle>
            <NPISectionDescription className="text-[#725242] max-w-3xl mx-auto">
              {description}
            </NPISectionDescription>
          </NPISectionHeader>
          <div className="flex justify-center items-center py-20">
            <Loader2 className="h-8 w-8 animate-spin text-[#25718A]" />
            <span className="ml-2 text-[#725242]">Loading projects...</span>
          </div>
        </div>
      </NPISection>
    )
  }

  // Error state
  if (error) {
    return (
      <NPISection className="py-24 bg-[#FFFFFF]">
        <div className="container mx-auto px-4">
          <NPISectionHeader className="text-center mb-16">
            <NPISectionTitle className="text-black mb-4">{title}</NPISectionTitle>
            <NPISectionDescription className="text-[#725242] max-w-3xl mx-auto">
              {description}
            </NPISectionDescription>
          </NPISectionHeader>
          <div className="text-center py-20">
            <p className="text-red-600 mb-4">Failed to load projects: {error}</p>
            <NPIButton
              onClick={() => window.location.reload()}
              className="bg-[#25718A] text-white hover:bg-[#1e5a6b]"
            >
              Try Again
            </NPIButton>
          </div>
        </div>
      </NPISection>
    )
  }

  return (
    <NPISection className="py-24 bg-[#FFFFFF]">
      <div className="container mx-auto px-4">
        <NPISectionHeader className="text-center mb-16">
          <NPISectionTitle className="text-black mb-4">{title}</NPISectionTitle>
          <NPISectionDescription className="text-[#725242] max-w-3xl mx-auto">
            {description}
          </NPISectionDescription>
        </NPISectionHeader>

        {/* Filter Buttons */}
        {categories.length > 1 && (
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {categories.map((category) => (
              <NPIButton
                key={category}
                variant={filter === category ? 'primary' : 'outline'}
                onClick={() => setFilter(category)}
                className={`${
                  filter === category
                    ? 'bg-[#8A3E25] text-white border-[#8A3E25]'
                    : 'border-[#25718A] text-[#25718A] hover:bg-[#25718A] hover:text-white'
                } transition-all duration-300`}
              >
                {category === 'all' ? 'All Projects' : category}
              </NPIButton>
            ))}
          </div>
        )}

        {/* Empty state */}
        {projects.length === 0 && !loading && (
          <div className="text-center py-20">
            <p className="text-[#725242] text-lg mb-4">No projects found.</p>
            <p className="text-[#725242]/70">Check back later for new projects and initiatives.</p>
          </div>
        )}

        {/* Projects Grid - Square cards */}
        {projects.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredProjects.map((project, index) => (
              <motion.div
                key={project.id}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ scale: 1.05, y: -10 }}
                className="aspect-square"
              >
                <NPICard className="h-full w-full overflow-hidden hover:shadow-2xl transition-all duration-300 border-2 border-transparent hover:border-[#725242]/30 flex flex-col">
                  {/* Square Image Section - Takes up top 45% */}
                  <div className="relative h-[45%] w-full flex-shrink-0">
                    <Image
                      src={project.image || '/assets/product 1.jpg'}
                      alt={project.imageMetadata?.alt || project.title}
                      fill
                      className="object-cover"
                    />
                    <div className="absolute top-2 left-2">
                      <span
                        className={`px-2 py-1 text-xs font-medium ${getStatusColor(project.status)}`}
                      >
                        {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
                      </span>
                    </div>
                    <div className="absolute top-2 right-2">
                      <span
                        className={`px-2 py-1 text-xs font-bold ${getCategoryColor(project.category).bg} ${getCategoryColor(project.category).text} border ${getCategoryColor(project.category).border}`}
                      >
                        {project.category}
                      </span>
                    </div>
                  </div>

                  {/* Content Section - Takes up remaining 55% */}
                  <div className="h-[55%] p-3 flex flex-col justify-between">
                    {/* Title - More space */}
                    <div className="flex-1 flex flex-col justify-center">
                      <h3 className="text-black text-sm font-bold mb-3 line-clamp-3 leading-tight text-center">
                        {project.title}
                      </h3>
                    </div>

                    {/* Essential Details Only */}
                    <div className="space-y-2 mb-3">
                      <div className="flex items-center justify-center gap-2 text-xs text-[#725242]">
                        <MapPin className="w-3 h-3 text-[#8A3E25] flex-shrink-0" />
                        <span className="font-medium">{project.location}</span>
                      </div>
                      <div className="text-center">
                        <span className="text-xs text-[#725242] font-medium">
                          {project.duration}
                        </span>
                      </div>
                    </div>

                    {/* Button - Smaller */}
                    <NPIButton
                      onClick={() => router.push(`/projects/${project.id}`)}
                      className="w-full bg-[#8A3E25] hover:bg-[#25718A] text-white font-medium transition-all duration-300 border-2 border-[#8A3E25] hover:border-[#25718A] text-xs py-1.5"
                    >
                      View Details
                    </NPIButton>
                  </div>
                </NPICard>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </NPISection>
  )
}
