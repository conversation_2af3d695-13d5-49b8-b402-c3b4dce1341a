'use client'

import React, { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import { NPICard, NPICardTitle } from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import Image from 'next/image'
import Link from 'next/link'
import { Calendar, ArrowRight, Loader2 } from 'lucide-react'

interface CMSNewsItem {
  id: string
  title: string
  subtitle?: string
  summary: string
  featuredImage?: {
    image?: {
      url?: string
      alt?: string
    }
    alt?: string
    caption?: string
  }
  publishDate: string
  category: string
  status: string
  featured?: boolean
  urgent?: boolean
  slug: string
}

interface NPILatestUpdatesProps {
  title?: string
  description?: string
  limit?: number
}

export const NPILatestUpdatesBlock: React.FC<NPILatestUpdatesProps> = ({
  title = 'News & Updates',
  description = "Stay informed about our latest developments, achievements, and upcoming initiatives in Kenya's natural products sector.",
  limit = 4,
}) => {
  const [news, setNews] = useState<CMSNewsItem[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchLatestNews = async () => {
      try {
        setLoading(true)
        setError(null)

        const response = await fetch(`/api/news?limit=${limit}&sort=-publishDate`)

        if (!response.ok) {
          throw new Error(`Failed to fetch news: ${response.status}`)
        }

        const data = await response.json()

        if (data.success && data.news) {
          setNews(data.news)
        } else {
          throw new Error('Invalid response format')
        }
      } catch (err) {
        console.error('Error fetching latest news:', err)
        setError(err instanceof Error ? err.message : 'Failed to load news')
      } finally {
        setLoading(false)
      }
    }

    fetchLatestNews()
  }, [limit])

  // Helper function to get image URL
  const getImageUrl = (newsItem: CMSNewsItem): string => {
    if (newsItem.featuredImage?.image?.url) {
      return newsItem.featuredImage.image.url
    }
    // Fallback to a default image
    return '/assets/news-placeholder.jpg'
  }

  // Helper function to get image alt text
  const getImageAlt = (newsItem: CMSNewsItem): string => {
    return newsItem.featuredImage?.alt || newsItem.featuredImage?.image?.alt || newsItem.title
  }

  // Helper function to format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  // Helper function to format category
  const formatCategory = (category: string): string => {
    return category.split('-').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')
  }

  const featuredNews = news.find((item) => item.featured)
  const regularNews = news.filter((item) => !item.featured)

  return (
    <NPISection size="sm" className="bg-white relative overflow-hidden pb-0">
      <div className="relative z-10">
        <NPISectionHeader className="text-center mb-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="inline-flex items-center px-4 py-2 bg-[#8A3E25]/15 border border-[#8A3E25]/30 text-[#8A3E25] text-sm font-semibold mb-3"
          >
            <Calendar className="w-4 h-4 mr-3 text-[#25718A]" />
            News & Updates
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <NPISectionTitle className="leading-[1.1] tracking-[-0.02em] mb-2 text-black font-bold text-2xl lg:text-3xl">
              {title}
            </NPISectionTitle>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <NPISectionDescription className="font-light leading-[1.6] text-black max-w-xl mx-auto text-sm lg:text-base">
              {description}
            </NPISectionDescription>
          </motion.div>
        </NPISectionHeader>

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-[#8A3E25]" />
            <span className="ml-2 text-[#725242]">Loading latest news...</span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="text-center py-12">
            <p className="text-red-600 mb-4">Failed to load news: {error}</p>
            <NPIButton
              onClick={() => window.location.reload()}
              variant="outline"
              className="border-[#8A3E25] text-[#8A3E25] hover:bg-[#8A3E25] hover:text-white"
            >
              Try Again
            </NPIButton>
          </div>
        )}

        {/* News Grid */}
        {!loading && !error && news.length > 0 && (
          <div className="grid lg:grid-cols-3 gap-4 mb-4 max-w-4xl mx-auto">
            {/* Regular News - Square Cards */}
            {regularNews.slice(0, 3).map((item, index) => (
            <motion.div
              key={item.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              whileHover={{ y: -6 }}
            >
              <NPICard
                className={`overflow-hidden shadow-lg border-2 hover:shadow-xl group transition-all duration-300 aspect-square flex flex-col hover:scale-[1.03] hover:-translate-y-1 ${
                  index % 3 === 0
                    ? 'bg-[#725242] border-[#725242] hover:border-[#8A3E25] hover:shadow-[#725242]/30'
                    : index % 3 === 1
                      ? 'bg-[#8A3E25] border-[#8A3E25] hover:border-[#725242] hover:shadow-[#8A3E25]/30'
                      : 'bg-white border-white hover:border-[#8A3E25] hover:shadow-white/30'
                }`}
              >
                <div className="relative h-1/2 w-full flex-shrink-0">
                  <Image
                    src={getImageUrl(item)}
                    alt={getImageAlt(item)}
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-black/40" />
                  <div className="absolute top-2 right-2">
                    <span
                      className={`px-2 py-1 text-xs font-bold text-white ${
                        index % 3 === 0
                          ? 'bg-[#25718A]'
                          : index % 3 === 1
                            ? 'bg-[#725242]'
                            : 'bg-[#8A3E25]'
                      }`}
                    >
                      {formatCategory(item.category)}
                    </span>
                  </div>
                  {item.urgent && (
                    <div className="absolute top-2 left-2">
                      <span className="px-2 py-1 text-xs font-bold text-white bg-red-600">
                        URGENT
                      </span>
                    </div>
                  )}
                </div>

                <div className="h-1/2 p-3 flex flex-col justify-between">
                  <div>
                    <div
                      className={`flex items-center gap-2 text-xs mb-2 ${
                        index % 3 === 0
                          ? 'text-white/90'
                          : index % 3 === 1
                            ? 'text-white/90'
                            : 'text-black/80'
                      }`}
                    >
                      <Calendar className="w-3 h-3 text-[#25718A]" />
                      <span className="font-medium">{formatDate(item.publishDate)}</span>
                    </div>

                    <h3
                      className={`font-bold text-sm leading-tight mb-2 line-clamp-2 ${
                        index % 3 === 0
                          ? 'text-white'
                          : index % 3 === 1
                            ? 'text-white'
                            : 'text-black'
                      }`}
                    >
                      <Link href={`/news/${item.slug}`} className="transition-colors">
                        {item.title}
                      </Link>
                    </h3>
                    {item.subtitle && (
                      <p
                        className={`text-xs leading-tight line-clamp-1 ${
                          index % 3 === 0
                            ? 'text-white/80'
                            : index % 3 === 1
                              ? 'text-white/80'
                              : 'text-black/70'
                        }`}
                      >
                        {item.subtitle}
                      </p>
                    )}
                  </div>

                  <NPIButton
                    asChild
                    className="w-full bg-[#25718A] hover:bg-[#25718A] text-white font-medium transition-all duration-300 text-xs py-2"
                  >
                    <Link href={`/news/${item.slug}`} className="flex items-center justify-center gap-1">
                      Read More <ArrowRight className="w-3 h-3" />
                    </Link>
                  </NPIButton>
                </div>
              </NPICard>
            </motion.div>
          ))}
          </div>
        )}

        {/* Empty State */}
        {!loading && !error && news.length === 0 && (
          <div className="text-center py-12">
            <p className="text-[#725242] mb-4">No news updates available at the moment.</p>
            <Link href="/news">
              <NPIButton
                variant="outline"
                className="border-[#8A3E25] text-[#8A3E25] hover:bg-[#8A3E25] hover:text-white"
              >
                View All News
              </NPIButton>
            </Link>
          </div>
        )}

        {/* View All Button */}
        {!loading && !error && news.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-center mt-4"
          >
            <NPIButton
              asChild
              size="lg"
              className="bg-[#8A3E25] hover:bg-[#8A3E25] text-white font-bold px-8 py-3 transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              <Link href="/news">View All News & Updates</Link>
            </NPIButton>
          </motion.div>
        )}
      </div>
    </NPISection>
  )
}
