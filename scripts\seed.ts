import type { Payload } from 'payload'
import { seedComprehensiveCMSData } from '../src/lib/seed/comprehensive-cms-data'

export default async function seed(payload: Payload) {
  payload.logger.info('🌱 Starting CMS database seeding...')
  
  try {
    await seedComprehensiveCMSData(payload)
    
    payload.logger.info('🎉 CMS database seeding completed successfully!')
    payload.logger.info('')
    payload.logger.info('📊 The following collections have been populated:')
    payload.logger.info('  • Counties (47 entries)')
    payload.logger.info('  • Media (sample images)')
    payload.logger.info('  • Projects (4 entries)')
    payload.logger.info('  • Success Stories (4 entries)')
    payload.logger.info('  • Events (8 entries)')
    payload.logger.info('  • News (4 entries)')
    payload.logger.info('  • Resources (4 entries)')
    payload.logger.info('  • Partners (4 entries)')
    payload.logger.info('  • Investment Opportunities (4 entries)')
    payload.logger.info('  • Media Gallery (4 entries)')
    payload.logger.info('')
    payload.logger.info('🌐 You can now:')
    payload.logger.info('  1. Visit the CMS admin at http://localhost:3000/admin')
    payload.logger.info('  2. View the populated content in the frontend')
    payload.logger.info('  3. Test the API endpoints with real data')
    
  } catch (error) {
    payload.logger.error('❌ Error seeding database:', error)
    throw error
  }
}
