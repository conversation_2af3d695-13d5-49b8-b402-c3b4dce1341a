'use client'

import React, { useState, useEffect } from 'react'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import { NPICard, NPICardHeader, NPICardTitle, NPICardContent } from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import Link from 'next/link'
import Image from 'next/image'
import { Play, Calendar, Eye, ExternalLink, Video } from 'lucide-react'

interface MediaItem {
  id: string
  title: string
  description: string
  type: 'video' | 'photo' | 'document'
  thumbnail: string
  url: string
  date: string
  views?: number
  duration?: string
  category: string
}

interface NPIMediaGalleryProps {
  title?: string
  description?: string
  mediaItems?: MediaItem[]
}

export const NPIMediaGalleryBlock: React.FC<NPIMediaGalleryProps> = ({
  title = 'Media Gallery',
  description = "Explore our collection of videos, photos, and media coverage showcasing NPI's impact across Kenya's natural products sector.",
}) => {
  const [mediaItems, setMediaItems] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchMediaItems = async () => {
      try {
        setLoading(true)
        setError(null)

        const response = await fetch('/api/media-gallery?limit=12')

        if (!response.ok) {
          throw new Error(`Failed to fetch media items: ${response.status}`)
        }

        const data = await response.json()

        if (data.success && data.mediaItems) {
          setMediaItems(data.mediaItems)
        } else {
          throw new Error('Invalid response format')
        }
      } catch (err) {
        console.error('Error fetching media items:', err)
        setError(err instanceof Error ? err.message : 'Failed to load media items')
        // Fallback to empty array
        setMediaItems([])
      } finally {
        setLoading(false)
      }
    }

    fetchMediaItems()
  }, [])

  // Helper functions for CMS data
  const getMediaThumbnail = (mediaItem: any): string => {
    // Try thumbnail first
    if (mediaItem.thumbnail?.url) {
      return mediaItem.thumbnail.url
    }
    // Try main media as fallback
    if (mediaItem.media?.url) {
      return mediaItem.media.url
    }
    // Database media fallback
    if (mediaItem.media?.id) {
      return `/api/media/database/${mediaItem.media.id}`
    }
    // Final fallback to default thumbnail
    return '/assets/background.jpg'
  }

  const getMediaUrl = (mediaItem: any): string => {
    // For videos, we might want to link to a video player or external URL
    // For now, return a placeholder or the media file itself
    if (mediaItem.media?.url) {
      return mediaItem.media.url
    }
    if (mediaItem.media?.id) {
      return `/api/media/database/${mediaItem.media.id}`
    }
    return '#'
  }

  const getMediaDescription = (mediaItem: any): string => {
    // Try caption first (shorter description)
    if (mediaItem.caption) {
      return mediaItem.caption
    }

    // Then try full description
    if (mediaItem.description?.root?.children) {
      const extractText = (node: any): string => {
        if (node.type === 'text') {
          return node.text || ''
        }
        if (node.children) {
          return node.children.map(extractText).join('')
        }
        return ''
      }
      return mediaItem.description.root.children.map(extractText).join(' ').slice(0, 200) + '...'
    }
    return mediaItem.title
  }

  const getMediaDate = (mediaItem: any): string => {
    if (mediaItem.dateCreated) {
      return new Date(mediaItem.dateCreated).toLocaleDateString()
    }
    if (mediaItem.createdAt) {
      return new Date(mediaItem.createdAt).toLocaleDateString()
    }
    return 'Unknown date'
  }

  const getMediaDuration = (mediaItem: any): string | undefined => {
    return mediaItem.technical?.duration
  }

  const getMediaViews = (mediaItem: any): number => {
    return mediaItem.analytics?.viewCount || 0
  }

  if (loading) {
    return (
      <NPISection>
        <NPISectionHeader>
          <NPISectionTitle>{title}</NPISectionTitle>
          <NPISectionDescription>{description}</NPISectionDescription>
        </NPISectionHeader>
        <div className="flex justify-center items-center py-12">
          <div className="text-muted-foreground">Loading media gallery...</div>
        </div>
      </NPISection>
    )
  }

  if (error) {
    return (
      <NPISection>
        <NPISectionHeader>
          <NPISectionTitle>{title}</NPISectionTitle>
          <NPISectionDescription>{description}</NPISectionDescription>
        </NPISectionHeader>
        <div className="flex justify-center items-center py-12">
          <div className="text-red-600">Error: {error}</div>
        </div>
      </NPISection>
    )
  }

  if (mediaItems.length === 0) {
    return (
      <NPISection>
        <NPISectionHeader>
          <NPISectionTitle>{title}</NPISectionTitle>
          <NPISectionDescription>{description}</NPISectionDescription>
        </NPISectionHeader>
        <div className="flex justify-center items-center py-12">
          <div className="text-muted-foreground">No media items available.</div>
        </div>
      </NPISection>
    )
  }
  const getYouTubeVideoId = (url: string): string | null => {
    const match = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/)
    return match ? match[1] : null
  }

  const getYouTubeEmbedUrl = (url: string): string => {
    const videoId = getYouTubeVideoId(url)
    return videoId ? `https://www.youtube.com/embed/${videoId}` : url
  }

  const formatViews = (views: number): string => {
    if (views >= 1000000) {
      return `${(views / 1000000).toFixed(1)}M`
    } else if (views >= 1000) {
      return `${(views / 1000).toFixed(1)}K`
    }
    return views.toString()
  }

  const categories = Array.from(new Set(mediaItems.map((item) => item.category || 'General')))
  const featuredItem = mediaItems.find(item => item.featured) || mediaItems[0]

  return (
    <NPISection>
      <NPISectionHeader>
        <NPISectionTitle>{title}</NPISectionTitle>
        <NPISectionDescription>{description}</NPISectionDescription>
      </NPISectionHeader>

      {/* Category Filter */}
      <div className="flex flex-wrap gap-2 mb-8 justify-center">
        <NPIButton variant="outline" size="sm">
          All
        </NPIButton>
        {categories.map((category) => (
          <NPIButton key={category} variant="ghost" size="sm">
            {category}
          </NPIButton>
        ))}
      </div>

      {/* Featured Video */}
      {featuredItem && (
        <div className="mb-12">
          <h3 className="text-2xl font-bold mb-6 font-npi">Featured Video</h3>
          <NPICard className="overflow-hidden">
            <div className="grid lg:grid-cols-2 gap-0">
              <div className="relative aspect-video">
                <iframe
                  src={getYouTubeEmbedUrl(getMediaUrl(featuredItem))}
                  title={featuredItem.title}
                  className="w-full h-full"
                  allowFullScreen
                />
              </div>
              <div className="p-6">
                <div className="flex items-center gap-2 mb-3">
                  <Video className="w-5 h-5 text-red-600" />
                  <span className="text-sm text-muted-foreground font-npi">
                    {featuredItem.category || 'General'}
                  </span>
                </div>
                <NPICardTitle className="text-xl mb-3">{featuredItem.title}</NPICardTitle>
                <p className="text-muted-foreground mb-4 font-npi">{getMediaDescription(featuredItem)}</p>

                <div className="flex items-center gap-4 text-sm text-muted-foreground mb-4">
                  <span className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    {getMediaDate(featuredItem)}
                  </span>
                  <span className="flex items-center gap-1">
                    <Eye className="w-4 h-4" />
                    {formatViews(getMediaViews(featuredItem))} views
                  </span>
                  {getMediaDuration(featuredItem) && <span>{getMediaDuration(featuredItem)}</span>}
                </div>

                <NPIButton asChild variant="primary">
                  <Link href={getMediaUrl(featuredItem)} target="_blank" rel="noopener noreferrer">
                    <ExternalLink className="w-4 h-4 mr-2" />
                    Watch on YouTube
                  </Link>
                </NPIButton>
              </div>
            </div>
          </NPICard>
        </div>
      )}

      {/* Video Grid */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {mediaItems.filter(item => item.id !== featuredItem?.id).map((item) => (
          <NPICard
            key={item.id}
            className="overflow-hidden hover:shadow-lg transition-shadow duration-300"
          >
            <div className="relative aspect-video group cursor-pointer">
              <Image
                src={getMediaThumbnail(item)}
                alt={item.title}
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div className="absolute inset-0 bg-black/40 group-hover:bg-black/30 transition-colors duration-300" />
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-16 h-16 bg-[#25718A] rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <Play className="w-8 h-8 text-white ml-1" />
                </div>
              </div>
              {getMediaDuration(item) && (
                <div
                  className="absolute bottom-2 right-2 bg-black/80 text-white px-2 py-1 text-xs"
                  style={{ borderRadius: '0' }}
                >
                  {getMediaDuration(item)}
                </div>
              )}
              <div className="absolute top-2 left-2">
                <span
                  className="bg-[#8A3E25] text-white px-2 py-1 text-xs font-medium"
                  style={{ borderRadius: '0' }}
                >
                  {item.category || 'General'}
                </span>
              </div>
            </div>

            <NPICardHeader>
              <NPICardTitle className="text-lg leading-tight line-clamp-2">
                {item.title}
              </NPICardTitle>
            </NPICardHeader>

            <NPICardContent>
              <p className="text-muted-foreground text-sm leading-relaxed mb-4 line-clamp-2 font-npi">
                {getMediaDescription(item)}
              </p>

              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <span className="flex items-center gap-1">
                  <Calendar className="w-3 h-3" />
                  {getMediaDate(item)}
                </span>
                <span className="flex items-center gap-1">
                  <Eye className="w-3 h-3" />
                  {formatViews(getMediaViews(item))}
                </span>
              </div>
            </NPICardContent>
          </NPICard>
        ))}
      </div>

      {/* YouTube Channel CTA */}
      <div className="mt-12 text-center">
        <NPICard className="bg-gradient-to-r from-red-50 to-red-100 border-red-200">
          <NPICardContent className="p-8">
            <Video className="w-12 h-12 text-red-600 mx-auto mb-4" />
            <h3 className="text-2xl font-bold mb-4 font-npi">Subscribe to Our YouTube Channel</h3>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto font-npi">
              Stay updated with the latest videos, documentaries, and stories from Kenya&apos;s
              natural products sector. Subscribe to never miss our content!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <NPIButton asChild size="lg" className="bg-red-600 hover:bg-red-700">
                <Link
                  href="https://youtube.com/@npikenya"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Video className="w-5 h-5 mr-2" />
                  Subscribe Now
                </Link>
              </NPIButton>
              <NPIButton asChild size="lg" variant="outline">
                <Link href="/media/all-videos">View All Videos</Link>
              </NPIButton>
            </div>
          </NPICardContent>
        </NPICard>
      </div>
    </NPISection>
  )
}
