#!/usr/bin/env tsx

import { getPayload } from 'payload'
import config from '../src/payload.config'
import type { Payload } from 'payload'

function createRichText(text: string) {
  return {
    root: {
      type: 'root',
      children: [
        {
          type: 'paragraph',
          children: [
            {
              type: 'text',
              text: text,
            },
          ],
        },
      ],
    },
  }
}

async function createPlaceholderMedia(payload: Payload, filename: string, alt: string, category: string) {
  const placeholderImageBuffer = Buffer.from(
    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
    'base64'
  )
  
  return await payload.create({
    collection: 'media',
    data: {
      filename,
      mimeType: 'image/png',
      alt,
      category,
      description: `Placeholder ${alt}`,
      storageType: 'database',
    },
    file: {
      data: placeholderImageBuffer,
      mimetype: 'image/png',
      name: filename,
      size: placeholderImageBuffer.length,
    },
  })
}

async function seedPartners(payload: Payload) {
  payload.logger.info('🤝 Seeding Partners...')
  
  // Create placeholder logo
  const logoMedia = await createPlaceholderMedia(payload, 'partner-logo.png', 'Partner Logo', 'logos')
  
  const partnersData = [
    {
      name: 'Kenya Vision 2030',
      summary: "Kenya's development blueprint for transforming the country into a middle-income economy.",
      description: createRichText("Kenya's development blueprint for transforming the country into a middle-income economy through sustainable development initiatives."),
      logo: logoMedia.id,
      type: 'government',
      category: ['strategic'],
      status: 'active',
      contact: {
        website: 'https://vision2030.go.ke',
        primaryContact: {
          name: 'Vision 2030 Secretariat',
          role: 'Partnership Coordinator',
          email: '<EMAIL>',
        },
      },
      featured: true,
      verified: true,
    },
    {
      name: 'National Museums of Kenya',
      summary: 'Leading institution in cultural heritage preservation and traditional knowledge documentation.',
      description: createRichText('Leading institution in cultural heritage preservation and traditional knowledge documentation, supporting NPI in preserving indigenous knowledge systems.'),
      logo: logoMedia.id,
      type: 'academic',
      category: ['knowledge'],
      status: 'active',
      contact: {
        website: 'https://museums.or.ke',
        primaryContact: {
          name: 'NMK Partnership Office',
          role: 'Cultural Heritage Coordinator',
          email: '<EMAIL>',
        },
      },
      featured: true,
      verified: true,
    },
    {
      name: 'Kenya Association of Manufacturers',
      summary: 'Promoting manufacturing sector development and industrial growth in Kenya.',
      description: createRichText('Promoting manufacturing sector development and industrial growth in Kenya, supporting natural products commercialization and market access.'),
      logo: logoMedia.id,
      type: 'private-sector',
      category: ['implementation'],
      status: 'active',
      contact: {
        website: 'https://kam.co.ke',
        primaryContact: {
          name: 'KAM Partnerships',
          role: 'Industry Liaison Officer',
          email: '<EMAIL>',
        },
      },
      featured: false,
      verified: true,
    },
    {
      name: 'University of Nairobi',
      summary: 'Leading research institution partnering in natural products research and development.',
      description: createRichText('Leading research institution partnering in natural products research and development, providing scientific validation and research support.'),
      logo: logoMedia.id,
      type: 'academic',
      category: ['technical'],
      status: 'active',
      contact: {
        website: 'https://uonbi.ac.ke',
        primaryContact: {
          name: 'UoN Research Office',
          role: 'Research Coordinator',
          email: '<EMAIL>',
        },
      },
      featured: false,
      verified: true,
    },
  ]

  for (const partner of partnersData) {
    try {
      const created = await payload.create({
        collection: 'partners',
        data: partner,
      })
      payload.logger.info(`✅ Created partner: ${partner.name} (ID: ${created.id})`)
    } catch (error) {
      payload.logger.error(`❌ Failed to create partner ${partner.name}:`, error)
    }
  }
}

async function seedMediaGallery(payload: Payload) {
  payload.logger.info('📸 Seeding Media Gallery...')
  
  // Create placeholder media for gallery items
  const placeholderMedia = await createPlaceholderMedia(payload, 'gallery-media.png', 'Gallery Media', 'general')
  
  const mediaItems = [
    {
      title: 'NPI Documentary: Transforming Communities',
      description: createRichText('Comprehensive documentary showcasing how NPI initiatives are transforming communities across Kenya through natural products development.'),
      caption: 'Documentary showcasing community transformation',
      type: 'video',
      media: placeholderMedia.id,
      category: 'community-stories',
      dateCreated: new Date('2024-01-15').toISOString(),
      featured: true,
      published: true,
      technical: {
        duration: '12:45',
        format: 'MP4',
        quality: 'high'
      }
    },
    {
      title: 'Aloe Vera Cooperative Success Story',
      description: createRichText('Meet the women of Baringo County who transformed their community through traditional aloe vera knowledge.'),
      caption: 'Women cooperative success story',
      type: 'video',
      media: placeholderMedia.id,
      category: 'community-stories',
      dateCreated: new Date('2023-12-10').toISOString(),
      featured: true,
      published: true,
      technical: {
        duration: '8:30',
        format: 'MP4',
        quality: 'high'
      }
    },
    {
      title: 'Traditional Healers Share Their Wisdom',
      description: createRichText('Interviews with traditional healers from different communities about their knowledge and practices.'),
      caption: 'Traditional healers sharing wisdom',
      type: 'video',
      media: placeholderMedia.id,
      category: 'cultural-heritage',
      dateCreated: new Date('2023-10-15').toISOString(),
      featured: false,
      published: true,
      technical: {
        duration: '18:45',
        format: 'MP4',
        quality: 'high'
      }
    },
    {
      title: 'Youth Entrepreneurs Leading Change',
      description: createRichText('Feature story on young entrepreneurs who are innovating in the natural products sector.'),
      caption: 'Young entrepreneurs in natural products',
      type: 'video',
      media: placeholderMedia.id,
      category: 'natural-products',
      dateCreated: new Date('2023-09-20').toISOString(),
      featured: false,
      published: true,
      technical: {
        duration: '10:30',
        format: 'MP4',
        quality: 'high'
      }
    },
  ]

  for (const mediaItem of mediaItems) {
    try {
      const created = await payload.create({
        collection: 'media-gallery',
        data: mediaItem,
      })
      payload.logger.info(`✅ Created media item: ${mediaItem.title} (ID: ${created.id})`)
    } catch (error) {
      payload.logger.error(`❌ Failed to create media item ${mediaItem.title}:`, error)
    }
  }
}

async function runSeeding() {
  console.log('🌱 Starting comprehensive CMS data seeding...')
  
  try {
    const payload = await getPayload({ config })
    
    console.log('✅ Payload initialized successfully')
    
    // Run the seeding
    await seedPartners(payload)
    await seedMediaGallery(payload)
    
    // Verify the data was created
    console.log('\n📊 Verification:')
    const partnersCount = await payload.count({ collection: 'partners' })
    const mediaCount = await payload.count({ collection: 'media-gallery' })
    
    console.log(`Partners: ${partnersCount.totalDocs} entries`)
    console.log(`Media Gallery: ${mediaCount.totalDocs} entries`)
    
    console.log('🎉 Comprehensive CMS data seeding completed successfully!')
    process.exit(0)
    
  } catch (error) {
    console.error('❌ Error during seeding:', error)
    process.exit(1)
  }
}

// Run the seeding if this script is executed directly
import { fileURLToPath } from 'url'
import { dirname } from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

if (import.meta.url === `file://${process.argv[1]}`) {
  runSeeding()
}

export { runSeeding }
