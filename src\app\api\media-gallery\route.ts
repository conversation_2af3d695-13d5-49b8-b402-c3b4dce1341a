import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams
    const limit = parseInt(searchParams.get('limit') || '10')
    const page = parseInt(searchParams.get('page') || '1')
    const sort = searchParams.get('sort') || '-publishDate'
    const featured = searchParams.get('featured')
    const type = searchParams.get('type')
    const category = searchParams.get('category')

    // Build where clause
    const where: any = {}

    if (featured !== null) {
      where.featured = { equals: featured === 'true' }
    }

    if (type) {
      where.type = { equals: type }
    }

    if (category) {
      where.category = { equals: category }
    }

    // Fetch media items from CMS
    const result = await payload.find({
      collection: 'media-gallery',
      where,
      limit,
      page,
      sort: [sort],
      select: {
        id: true,
        title: true,
        description: true,
        caption: true,
        type: true,
        media: true,
        thumbnail: true,
        category: true,
        dateCreated: true,
        featured: true,
        published: true,
        technical: true,
        createdAt: true,
        updatedAt: true,
      },
    })

    return NextResponse.json({
      success: true,
      mediaItems: result.docs,
      pagination: {
        totalDocs: result.totalDocs,
        limit: result.limit,
        totalPages: result.totalPages,
        page: result.page,
        pagingCounter: result.pagingCounter,
        hasPrevPage: result.hasPrevPage,
        hasNextPage: result.hasNextPage,
        prevPage: result.prevPage,
        nextPage: result.nextPage,
      },
    })
  } catch (error) {
    console.error('Media Gallery API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Failed to fetch media items',
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const body = await request.json()

    // Create new media item
    const result = await payload.create({
      collection: 'media-gallery',
      data: body,
    })

    return NextResponse.json({
      success: true,
      mediaItem: result,
      message: 'Media item created successfully',
    })
  } catch (error) {
    console.error('Media Gallery POST API error:', error)

    // Check if it's a validation error
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation error',
          message: error.message,
          details: error.data || error.details || null,
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Failed to create media item',
        details: process.env.NODE_ENV === 'development' ? error.message : null,
      },
      { status: 500 }
    )
  }
}
