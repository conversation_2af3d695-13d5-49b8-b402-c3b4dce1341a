import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

export async function POST() {
  try {
    const payload = await getPayload({ config })
    
    // Create counties first
    const counties = [
      {
        name: 'Baringo',
        code: 'KE-030',
        coordinates: { latitude: 0.4684, longitude: 35.9737 },
        description: 'Known for its rich biodiversity and traditional aloe vera cultivation practices.',
        isActive: true,
      },
      {
        name: '<PERSON><PERSON><PERSON>',
        code: 'KE-022',
        coordinates: { latitude: -1.1719, longitude: 36.8356 },
        description: 'Agricultural hub with strong coffee and tea farming traditions.',
        isActive: true,
      },
    ]

    const createdCounties = []
    for (const county of counties) {
      const created = await payload.create({
        collection: 'counties',
        data: county,
      })
      createdCounties.push(created)
    }

    // Create a simple project without complex nested data
    const project = await payload.create({
      collection: 'projects',
      data: {
        title: 'Test Project',
        summary: 'A test project for seeding',
        description: {
          root: {
            children: [
              {
                children: [
                  {
                    detail: 0,
                    format: 0,
                    mode: 'normal',
                    style: '',
                    text: 'This is a test project created during seeding.',
                    type: 'text',
                    version: 1,
                  },
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                type: 'paragraph',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        },
        category: 'knowledge-preservation',
        pillar: 'indigenous-knowledge',
        status: 'active',
        featured: true,
        published: true,
        slug: 'test-project',
      },
    })
    
    return NextResponse.json({
      success: true,
      message: 'Simple seeding completed successfully',
      data: {
        counties: createdCounties.length,
        project: project.id,
      }
    })

  } catch (error) {
    console.error('Simple seed error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to run simple seeding',
        details: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    )
  }
}
