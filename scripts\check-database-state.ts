import type { Payload } from 'payload'

export default async function checkDatabaseState(payload: Payload) {
  payload.logger.info('🔍 Checking current database state...')
  
  try {
    const collections = [
      'counties',
      'projects', 
      'success-stories',
      'events',
      'news',
      'resources',
      'partners',
      'investment-opportunities',
      'media-gallery',
      'speakers',
      'partnerships',
      'contact-submissions',
      'partnership-applications',
      'media'
    ]

    payload.logger.info('📊 Current collection counts:')
    let totalEntries = 0

    for (const collectionName of collections) {
      try {
        const result = await payload.find({
          collection: collectionName as any,
          limit: 1,
          pagination: false,
        })
        
        const count = result.totalDocs || 0
        payload.logger.info(`   • ${collectionName}: ${count} entries`)
        totalEntries += count
        
        // Show a sample entry if it exists
        if (result.docs && result.docs.length > 0) {
          const sample = result.docs[0]
          payload.logger.info(`     Sample: ${sample.title || sample.name || sample.id}`)
        }
      } catch (error) {
        payload.logger.error(`   • ${collectionName}: Error - ${error.message}`)
      }
    }

    payload.logger.info(`\n📈 Total entries across all collections: ${totalEntries}`)
    
    if (totalEntries === 0) {
      payload.logger.warn('⚠️  No data found in any collection. Database may be empty.')
    } else {
      payload.logger.info('✅ Database contains data.')
    }
    
  } catch (error) {
    payload.logger.error('❌ Error checking database state:', error)
    throw error
  }
}
