import type { CollectionBefore<PERSON><PERSON>eHook } from 'payload'
import sharp from 'sharp'

export const storeImageInDatabase: CollectionBeforeChangeHook = async ({ data, operation, req }) => {
  // Only process for create operations and when we have file data
  if (operation === 'create' && req.file) {
    try {
      const file = req.file
      
      // Store original filename
      data.originalFilename = file.name
      
      // Set storage type to database
      data.storageType = 'database'
      
      // Check if it's an image file
      const isImage = file.mimetype?.startsWith('image/')
      
      if (isImage && file.data) {
        // Process image with <PERSON> to optimize and convert to base64
        let processedBuffer: Buffer
        
        try {
          // Optimize image: convert to WebP, resize if too large, compress
          processedBuffer = await sharp(file.data)
            .resize(2048, 2048, { 
              fit: 'inside', 
              withoutEnlargement: true 
            })
            .webp({ 
              quality: 85,
              effort: 4 
            })
            .toBuffer()
          
          // Update mimetype to WebP since we converted it
          data.mimeType = 'image/webp'
          
        } catch (sharpError) {
          req.payload.logger.warn('Sharp processing failed, using original file:', sharpError)
          processedBuffer = file.data
        }
        
        // Convert to base64 and ensure it's a string
        const base64Data = processedBuffer.toString('base64')

        // Store base64 data in the database - ensure it's a string
        data.base64Data = String(base64Data)
        
        // Update file size to processed size
        data.filesize = processedBuffer.length
        
        // Create a data URL for the file
        data.url = `data:${data.mimeType};base64,${base64Data}`
        
        req.payload.logger.info(`Stored image in database: ${data.mediaId} (${data.filesize} bytes)`)
        
      } else if (file.data) {
        // For non-image files, store as base64 but don't process
        const base64Data = file.data.toString('base64')
        data.base64Data = String(base64Data)
        data.url = `data:${file.mimetype};base64,${base64Data}`
        
        req.payload.logger.info(`Stored file in database: ${data.mediaId} (${file.data.length} bytes)`)
      }
      
    } catch (error) {
      req.payload.logger.error('Error storing file in database:', error)
      throw new Error('Failed to store file in database')
    }
  }
  
  return data
}
