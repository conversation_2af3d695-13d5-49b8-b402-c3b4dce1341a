#!/usr/bin/env tsx

import { getPayload } from 'payload'
import config from '../src/payload.config'
import { seedCMSData } from '../src/lib/seed/cms-data-new'

async function runSeeding() {
  console.log('🌱 Starting CMS data seeding...')
  
  try {
    const payload = await getPayload({ config })
    
    console.log('✅ Payload initialized successfully')
    
    // Run the seeding
    await seedCMSData(payload)
    
    console.log('🎉 CMS data seeding completed successfully!')
    process.exit(0)
    
  } catch (error) {
    console.error('❌ Error during seeding:', error)
    process.exit(1)
  }
}

// Run the seeding if this script is executed directly
if (require.main === module) {
  runSeeding()
}

export { runSeeding }
