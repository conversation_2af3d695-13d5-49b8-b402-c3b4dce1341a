// Comprehensive CMS Testing Script
// Tests all collections, API endpoints, and admin functionality

const BASE_URL = process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:3000'

// Test configuration
const TEST_CONFIG = {
  timeout: 10000,
  retries: 3,
  collections: [
    // Existing collections
    { name: 'projects', endpoint: '/api/projects', expectedFields: ['title', 'summary', 'category'] },
    { name: 'success-stories', endpoint: '/api/success-stories', expectedFields: ['title', 'summary', 'category'] },
    { name: 'news', endpoint: '/api/news', expectedFields: ['title', 'summary', 'category'] },
    { name: 'events', endpoint: '/api/events', expectedFields: ['title', 'date', 'type'] },
    { name: 'resources', endpoint: '/api/resources', expectedFields: ['title', 'type', 'category'] },
    { name: 'partners', endpoint: '/api/partners', expectedFields: ['name', 'description', 'type'] },
    { name: 'investment-opportunities', endpoint: '/api/investment-opportunities', expectedFields: ['title', 'description', 'sector'] },
    { name: 'media-gallery', endpoint: '/api/media-gallery', expectedFields: ['title', 'description', 'category'] },
    { name: 'partnerships', endpoint: '/api/partnerships', expectedFields: ['title', 'description', 'type'] },
    { name: 'counties', endpoint: '/api/counties', expectedFields: ['name', 'code'] },
    
    // New collections
    { name: 'strategic-pillars', endpoint: '/api/strategic-pillars', expectedFields: ['title', 'pillarId', 'summary'] },
    { name: 'page-content', endpoint: '/api/page-content', expectedFields: ['title', 'pageSlug', 'sectionKey'] },
    { name: 'statistics', endpoint: '/api/statistics', expectedFields: ['label', 'value', 'category'] },
  ]
}

// Utility functions
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms))

const fetchWithRetry = async (url, options = {}, retries = TEST_CONFIG.retries) => {
  for (let i = 0; i < retries; i++) {
    try {
      const response = await fetch(url, {
        ...options,
        timeout: TEST_CONFIG.timeout,
      })
      return response
    } catch (error) {
      if (i === retries - 1) throw error
      await delay(1000 * (i + 1)) // Exponential backoff
    }
  }
}

// Test functions
async function testEndpoint(collection) {
  console.log(`\n🧪 Testing ${collection.name}...`)
  
  try {
    const response = await fetchWithRetry(`${BASE_URL}${collection.endpoint}?limit=5`)
    const data = await response.json()
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${data.message || data.error || 'Unknown error'}`)
    }
    
    // Check response structure
    if (!data.success) {
      throw new Error(`API returned success: false - ${data.message || 'Unknown error'}`)
    }
    
    // Check data structure based on collection type
    let items = []
    if (data.projects) items = data.projects
    else if (data.stories) items = data.stories
    else if (data.news) items = data.news
    else if (data.events) items = data.events
    else if (data.resources) items = data.resources
    else if (data.partners) items = data.partners
    else if (data.opportunities) items = data.opportunities
    else if (data.media) items = data.media
    else if (data.partnerships) items = data.partnerships
    else if (data.counties) items = data.counties
    else if (data.pillars) items = data.pillars
    else if (data.content) items = data.content
    else if (data.statistics) items = data.statistics
    
    console.log(`   ✅ Success: ${items.length} items found`)
    
    // Validate expected fields if items exist
    if (items.length > 0 && collection.expectedFields) {
      const firstItem = items[0]
      const missingFields = collection.expectedFields.filter(field => !(field in firstItem))
      
      if (missingFields.length > 0) {
        console.log(`   ⚠️  Missing fields: ${missingFields.join(', ')}`)
      } else {
        console.log(`   ✅ All expected fields present`)
      }
    }
    
    // Check pagination
    if (data.pagination) {
      console.log(`   📊 Pagination: ${data.pagination.totalDocs} total, page ${data.pagination.page}`)
    }
    
    return { success: true, count: items.length }
    
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`)
    return { success: false, error: error.message }
  }
}

async function testSpecificEndpoints() {
  console.log('\n🔍 Testing Specific Endpoint Features...')
  
  const specificTests = [
    {
      name: 'Strategic Pillars by ID',
      url: '/api/strategic-pillars/knowledge-documentation',
      description: 'Test pillar lookup by pillarId'
    },
    {
      name: 'Page Content Filtering',
      url: '/api/page-content?pageSlug=home&sectionKey=hero',
      description: 'Test page content filtering'
    },
    {
      name: 'Featured Statistics',
      url: '/api/statistics?featured=true',
      description: 'Test featured statistics filtering'
    },
    {
      name: 'Statistics by Category',
      url: '/api/statistics?category=impact',
      description: 'Test statistics category filtering'
    },
    {
      name: 'Featured Projects',
      url: '/api/projects?featured=true&limit=3',
      description: 'Test featured projects (regression test)'
    }
  ]
  
  for (const test of specificTests) {
    console.log(`\n   🎯 ${test.name}: ${test.description}`)
    try {
      const response = await fetchWithRetry(`${BASE_URL}${test.url}`)
      const data = await response.json()
      
      if (response.ok && data.success) {
        console.log(`      ✅ Success`)
      } else {
        console.log(`      ❌ Failed: ${data.message || data.error || 'Unknown error'}`)
      }
    } catch (error) {
      console.log(`      💥 Request failed: ${error.message}`)
    }
  }
}

async function generateTestReport(results) {
  console.log('\n📊 COMPREHENSIVE CMS TEST REPORT')
  console.log('=' .repeat(60))
  
  const successful = results.filter(r => r.success)
  const failed = results.filter(r => !r.success)
  const totalItems = results.reduce((sum, r) => sum + (r.count || 0), 0)
  
  console.log(`\n📈 SUMMARY:`)
  console.log(`   Collections Tested: ${results.length}`)
  console.log(`   Successful: ${successful.length} ✅`)
  console.log(`   Failed: ${failed.length} ${failed.length > 0 ? '❌' : '✅'}`)
  console.log(`   Total Items Found: ${totalItems}`)
  console.log(`   Success Rate: ${Math.round((successful.length / results.length) * 100)}%`)
  
  if (failed.length > 0) {
    console.log(`\n❌ FAILED COLLECTIONS:`)
    failed.forEach(result => {
      console.log(`   • ${result.collection}: ${result.error}`)
    })
  }
  
  console.log(`\n🎯 RECOMMENDATIONS:`)
  if (failed.length === 0) {
    console.log(`   ✅ All collections are working perfectly!`)
    console.log(`   ✅ CMS system is ready for production`)
    console.log(`   ✅ Admin interface should be fully functional`)
  } else {
    console.log(`   🔧 Fix failed collections before production deployment`)
    console.log(`   🧪 Run individual collection tests for debugging`)
  }
  
  if (totalItems === 0) {
    console.log(`   📝 Consider running seed scripts to populate collections`)
    console.log(`   📝 Test admin interface for content creation`)
  }
  
  console.log(`\n🚀 NEXT STEPS:`)
  console.log(`   1. Test admin interface at ${BASE_URL}/admin`)
  console.log(`   2. Create sample content through admin interface`)
  console.log(`   3. Verify frontend displays CMS content correctly`)
  console.log(`   4. Run performance tests under load`)
}

// Main test execution
async function runComprehensiveTest() {
  console.log('🚀 COMPREHENSIVE CMS TESTING STARTED')
  console.log(`🌐 Testing against: ${BASE_URL}`)
  console.log('⏱️  This may take a few minutes...\n')
  
  const results = []
  
  // Test all collections
  for (const collection of TEST_CONFIG.collections) {
    const result = await testEndpoint(collection)
    results.push({ ...result, collection: collection.name })
    await delay(500) // Prevent overwhelming the server
  }
  
  // Test specific endpoint features
  await testSpecificEndpoints()
  
  // Generate comprehensive report
  await generateTestReport(results)
  
  console.log('\n✅ COMPREHENSIVE CMS TESTING COMPLETED!')
}

// Run the tests
runComprehensiveTest().catch(error => {
  console.error('\n💥 Test execution failed:', error)
  process.exit(1)
})
