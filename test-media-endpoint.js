#!/usr/bin/env node

/**
 * Test script to check media endpoints
 * Run with: node test-media-endpoint.js
 */

const http = require('http')

function makeRequest(path) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      }
    }

    const req = http.request(options, (res) => {
      let data = ''
      
      res.on('data', (chunk) => {
        data += chunk
      })
      
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          data: data
        })
      })
    })

    req.on('error', (error) => {
      reject(error)
    })

    req.setTimeout(10000, () => {
      req.destroy()
      reject(new Error('Request timeout'))
    })

    req.end()
  })
}

async function testMediaEndpoints() {
  console.log('🧪 Testing Media Endpoints...\n')

  try {
    // First, get debug info about media
    console.log('1. Getting media debug info...')
    const debugResult = await makeRequest('/api/debug/media')
    
    if (debugResult.status === 200) {
      const debugData = JSON.parse(debugResult.data)
      console.log(`✅ Found ${debugData.totalDocs} media items`)
      
      if (debugData.media && debugData.media.length > 0) {
        console.log('\nMedia items:')
        debugData.media.forEach((media, index) => {
          console.log(`  ${index + 1}. ${media.filename} (${media.id})`)
          console.log(`     - Storage: ${media.storageType}`)
          console.log(`     - Has base64: ${media.hasBase64Data}`)
          console.log(`     - Base64 length: ${media.base64DataLength}`)
          console.log(`     - URL: ${media.url}`)
          console.log('')
        })

        // Test the first media item
        const firstMedia = debugData.media[0]
        if (firstMedia && firstMedia.hasBase64Data) {
          console.log(`2. Testing media endpoint for ${firstMedia.filename}...`)
          
          const mediaResult = await makeRequest(`/api/media/database/${firstMedia.id}`)
          console.log(`   Status: ${mediaResult.status}`)
          console.log(`   Content-Type: ${mediaResult.headers['content-type']}`)
          console.log(`   Content-Length: ${mediaResult.headers['content-length']}`)
          
          if (mediaResult.status === 200) {
            console.log('   ✅ Media endpoint working!')
          } else {
            console.log('   ❌ Media endpoint failed')
            console.log(`   Response: ${mediaResult.data}`)
          }
        } else {
          console.log('⚠️  No media items with base64 data found')
        }
      } else {
        console.log('⚠️  No media items found')
      }
    } else {
      console.log(`❌ Debug endpoint failed: ${debugResult.status}`)
      console.log(debugResult.data)
    }

    // Test projects debug info
    console.log('\n3. Getting projects debug info...')
    const projectsResult = await makeRequest('/api/debug/projects')
    
    if (projectsResult.status === 200) {
      const projectsData = JSON.parse(projectsResult.data)
      console.log(`✅ Found ${projectsData.totalDocs} projects`)
      
      if (projectsData.projects && projectsData.projects.length > 0) {
        console.log('\nProjects with images:')
        projectsData.projects.forEach((project, index) => {
          console.log(`  ${index + 1}. ${project.title}`)
          console.log(`     - Image: ${JSON.stringify(project.image, null, 2)}`)
          console.log('')
        })
      }
    } else {
      console.log(`❌ Projects debug endpoint failed: ${projectsResult.status}`)
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

// Check if server is running
makeRequest('/api/debug/media')
  .then(() => {
    testMediaEndpoints()
  })
  .catch(() => {
    console.log('❌ Server is not running on http://localhost:3000')
    console.log('💡 Please start the development server first with: npm run dev')
    process.exit(1)
  })
