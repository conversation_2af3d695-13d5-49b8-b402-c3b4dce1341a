import type { CollectionConfig } from 'payload'
import { authenticated } from '../../access/authenticated'
import { anyone } from '../../access/anyone'
import { generateUniversalId, getIdFieldConfig } from '../../hooks/generateUniversalId'
import { sanitizeForDatabase } from '../../hooks/sanitizeForDatabase'

export const Statistics: CollectionConfig = {
  slug: 'statistics',
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'label',
    defaultColumns: ['label', 'value', 'unit', 'category', 'published'],
    group: 'Content Management',
  },
  labels: {
    singular: 'Statistic',
    plural: 'Statistics',
  },
  fields: [
    getIdFieldConfig('statistics'),
    {
      name: 'statKey',
      type: 'text',
      required: true,
      unique: true,
      admin: {
        description: 'Unique identifier for this statistic (e.g., total-projects, counties-covered)',
      },
    },
    {
      name: 'label',
      type: 'text',
      required: true,
      admin: {
        description: 'Display label for this statistic (e.g., "Projects Completed")',
      },
    },
    {
      name: 'value',
      type: 'number',
      required: true,
      admin: {
        description: 'The numeric value of this statistic',
      },
    },
    {
      name: 'unit',
      type: 'text',
      admin: {
        description: 'Unit of measurement (e.g., %, K, M, counties, people)',
      },
    },
    {
      name: 'description',
      type: 'textarea',
      admin: {
        description: 'Description of what this statistic represents',
      },
    },
    {
      name: 'category',
      type: 'select',
      required: true,
      options: [
        { label: 'Impact & Outcomes', value: 'impact' },
        { label: 'Reach & Coverage', value: 'reach' },
        { label: 'Projects & Programs', value: 'projects' },
        { label: 'Partnerships', value: 'partners' },
        { label: 'Capacity Building', value: 'capacity' },
        { label: 'Knowledge Documentation', value: 'knowledge' },
        { label: 'Economic Development', value: 'economic' },
        { label: 'Community Engagement', value: 'community' },
      ],
      admin: {
        description: 'Category this statistic belongs to',
      },
    },
    {
      name: 'displayFormat',
      type: 'select',
      required: true,
      options: [
        { label: 'Number (1,234)', value: 'number' },
        { label: 'Percentage (45%)', value: 'percentage' },
        { label: 'Currency (KSh 1.2M)', value: 'currency' },
        { label: 'Abbreviated (1.2K)', value: 'abbreviated' },
        { label: 'Raw (1234)', value: 'raw' },
      ],
      defaultValue: 'number',
      admin: {
        description: 'How to format this number for display',
      },
    },
    {
      name: 'trend',
      type: 'group',
      fields: [
        {
          name: 'direction',
          type: 'select',
          options: [
            { label: 'Increasing', value: 'up' },
            { label: 'Decreasing', value: 'down' },
            { label: 'Stable', value: 'stable' },
            { label: 'Not Applicable', value: 'none' },
          ],
          defaultValue: 'none',
        },
        {
          name: 'percentage',
          type: 'number',
          admin: {
            description: 'Percentage change (positive or negative)',
          },
        },
        {
          name: 'period',
          type: 'text',
          admin: {
            description: 'Time period for the trend (e.g., "vs last year")',
          },
        },
      ],
      admin: {
        description: 'Trend information for this statistic',
      },
    },
    {
      name: 'source',
      type: 'group',
      fields: [
        {
          name: 'name',
          type: 'text',
          admin: {
            description: 'Source of this data (e.g., "NPI Database", "Annual Report 2024")',
          },
        },
        {
          name: 'url',
          type: 'text',
          admin: {
            description: 'URL to the data source (optional)',
          },
        },
        {
          name: 'lastVerified',
          type: 'date',
          admin: {
            description: 'When this data was last verified',
          },
        },
      ],
      admin: {
        description: 'Data source information',
      },
    },
    {
      name: 'displayOrder',
      type: 'number',
      defaultValue: 1,
      admin: {
        description: 'Order to display this statistic within its category',
      },
    },
    {
      name: 'featured',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Feature this statistic on key pages',
      },
    },
    {
      name: 'published',
      type: 'checkbox',
      defaultValue: true,
      admin: {
        description: 'Make this statistic visible to the public',
      },
    },
    {
      name: 'lastUpdated',
      type: 'date',
      admin: {
        readOnly: true,
        description: 'Last time this statistic was updated',
      },
    },
  ],
  hooks: {
    beforeChange: [
      generateUniversalId,
      sanitizeForDatabase,
      ({ data }) => {
        // Auto-update lastUpdated timestamp
        data.lastUpdated = new Date()
        return data
      },
    ],
  },
}

export default Statistics
