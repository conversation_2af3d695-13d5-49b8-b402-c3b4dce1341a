import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const published = searchParams.get('published') !== 'false'

    // Build where clause
    const where: any = {}
    
    if (published) {
      where.published = { equals: true }
    }

    // Fetch strategic pillars from PayloadCMS
    const result = await payload.find({
      collection: 'strategic-pillars',
      where,
      page,
      limit,
      sort: ['order'],
      populate: {
        relatedProjects: true,
      },
    })

    return NextResponse.json({
      success: true,
      pillars: result.docs,
      pagination: {
        totalDocs: result.totalDocs,
        totalPages: result.totalPages,
        page: result.page,
        limit: result.limit,
        hasNextPage: result.hasNextPage,
        hasPrevPage: result.hasPrevPage,
      }
    })
  } catch (error) {
    console.error('Strategic Pillars API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    )
  }
}
