import type { Payload } from 'payload'

export default async function seedWorkingData(payload: Payload) {
  payload.logger.info('🌱 Seeding working comprehensive data...')
  
  try {
    // Helper function to create rich text content
    const createRichText = (text: string) => ({
      root: {
        children: [
          {
            children: [
              {
                detail: 0,
                format: 0,
                mode: 'normal' as const,
                style: '',
                text,
                type: 'text' as const,
                version: 1,
              },
            ],
            direction: 'ltr' as const,
            format: '' as const,
            indent: 0,
            type: 'paragraph' as const,
            version: 1,
          },
        ],
        direction: 'ltr' as const,
        format: '' as const,
        indent: 0,
        type: 'root' as const,
        version: 1,
      },
    })

    // Create counties first
    payload.logger.info('Creating counties...')
    const counties = []
    
    const countyData = [
      { name: 'Nairobi', code: 'KE-047', lat: -1.2921, lng: 36.8219, desc: 'Capital city of Kenya' },
      { name: 'Kiam<PERSON>', code: 'KE-022', lat: -1.1719, lng: 36.8356, desc: 'Agricultural hub with coffee and tea farming' },
      { name: '<PERSON><PERSON><PERSON>', code: 'KE-017', lat: -1.5177, lng: 37.2634, desc: 'Home to traditional Kamba crafts' },
      { name: 'Turkana', code: 'KE-023', lat: 3.1167, lng: 35.5833, desc: 'Arid region with drought-resistant plants' },
    ]

    for (const county of countyData) {
      const created = await payload.create({
        collection: 'counties',
        data: {
          name: county.name,
          code: county.code,
          coordinates: { latitude: county.lat, longitude: county.lng },
          description: county.desc,
          isActive: true,
        },
      })
      counties.push(created)
      payload.logger.info(`✅ Created county: ${county.name}`)
    }

    // Create a simple media entry first (without file upload for now)
    payload.logger.info('Creating placeholder media...')
    const placeholderMedia = await payload.create({
      collection: 'media',
      data: {
        alt: 'Placeholder image',
        filename: 'placeholder.jpg',
        mimeType: 'image/jpeg',
        filesize: 100000,
        width: 800,
        height: 600,
        focalPoint: { x: 50, y: 50 },
        category: 'general',
        description: 'Placeholder image for seeding',
      },
    })
    payload.logger.info(`✅ Created placeholder media: ${placeholderMedia.id}`)

    // Create projects (5 entries)
    payload.logger.info('Creating projects...')
    const projectsData = [
      {
        title: 'Indigenous Knowledge Documentation Initiative',
        summary: 'Documenting and preserving traditional knowledge systems of indigenous communities across Kenya.',
        category: 'knowledge-preservation' as const,
        pillar: 'indigenous-knowledge' as const,
        status: 'active' as const,
        countyId: counties[0].id,
      },
      {
        title: 'Community-Led Natural Products Development',
        summary: 'Empowering communities to develop sustainable natural products businesses.',
        category: 'community-empowerment' as const,
        pillar: 'community-innovation' as const,
        status: 'active' as const,
        countyId: counties[1].id,
      },
      {
        title: 'Natural Products Research Hub',
        summary: 'Establishing a research and innovation hub for natural products development.',
        category: 'research-development' as const,
        pillar: 'capacity-building' as const,
        status: 'planning' as const,
        countyId: counties[1].id,
      },
      {
        title: 'Market Access Development Program',
        summary: 'Developing value chains to connect producers with markets.',
        category: 'market-development' as const,
        pillar: 'market-development' as const,
        status: 'active' as const,
        countyId: counties[2].id,
      },
      {
        title: 'Digital Knowledge Platform',
        summary: 'Creating a digital platform for traditional knowledge exchange.',
        category: 'technology-innovation' as const,
        pillar: 'technology-innovation' as const,
        status: 'active' as const,
        countyId: counties[3].id,
      },
    ]

    for (const project of projectsData) {
      const created = await payload.create({
        collection: 'projects',
        data: {
          title: project.title,
          description: createRichText(`${project.summary} This project aims to create lasting impact in communities across Kenya through sustainable development and community empowerment initiatives.`),
          summary: project.summary,
          image: {
            image: placeholderMedia.id,
            alt: `${project.title} project image`,
            caption: `Visual representation of the ${project.title}`,
          },
          category: project.category,
          pillar: project.pillar,
          status: project.status,
          location: {
            counties: [project.countyId],
            specificLocation: `${counties.find(c => c.id === project.countyId)?.name} County`,
            coordinates: { latitude: -1.0, longitude: 36.0 },
          },
          timeline: {
            startDate: '2024-01-01',
            endDate: '2025-12-31',
            duration: '2 years',
            milestones: [
              {
                title: 'Project Initiation',
                description: 'Project kickoff and team formation',
                targetDate: '2024-03-31',
                completed: true,
              },
              {
                title: 'Implementation Phase',
                description: 'Main project activities and deliverables',
                targetDate: '2024-12-31',
                completed: false,
              },
            ],
          },
          budget: {
            totalBudget: 15000000,
            currency: 'KES',
            fundingSources: [
              { source: 'Government of Kenya', amount: 10000000, percentage: 67 },
              { source: 'Development Partners', amount: 5000000, percentage: 33 },
            ],
          },
          impact: {
            beneficiaries: 2000,
            communities: 15,
            jobsCreated: 75,
            metrics: [
              { metric: 'Communities Reached', value: '15', unit: 'communities' },
              { metric: 'Jobs Created', value: '75', unit: 'jobs' },
            ],
          },
          team: {
            projectManager: 'Project Manager',
            keyPersonnel: [
              { name: 'Dr. Sarah Kimani', role: 'Technical Lead', organization: 'NPI' },
              { name: 'John Mwangi', role: 'Community Coordinator', organization: 'Local Partner' },
            ],
          },
          featured: true,
          published: true,
          tags: [
            { tag: 'natural products' },
            { tag: 'community development' },
            { tag: 'sustainable development' },
          ],
          slug: project.title.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, ''),
        },
      })
      payload.logger.info(`✅ Created project: ${project.title}`)
    }

    // Verify data
    const projectCount = await payload.find({ collection: 'projects', limit: 1 })
    const countyCount = await payload.find({ collection: 'counties', limit: 1 })
    const mediaCount = await payload.find({ collection: 'media', limit: 1 })

    payload.logger.info(`📊 Successfully created:`)
    payload.logger.info(`   Counties: ${countyCount.totalDocs}`)
    payload.logger.info(`   Projects: ${projectCount.totalDocs}`)
    payload.logger.info(`   Media: ${mediaCount.totalDocs}`)
    payload.logger.info('✅ Working data seeding completed!')

  } catch (error) {
    payload.logger.error('❌ Error in working data seeding:', error)
    throw error
  }
}
