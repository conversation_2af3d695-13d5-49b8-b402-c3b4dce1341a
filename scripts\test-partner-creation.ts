import type { Payload } from 'payload'

export default async function testPartnerCreation(payload: Payload) {
  payload.logger.info('🧪 Testing partner creation...')
  
  try {
    // Helper function to create rich text content
    const createRichText = (text: string) => ({
      root: {
        children: [
          {
            children: [
              {
                detail: 0,
                format: 0,
                mode: 'normal' as const,
                style: '',
                text,
                type: 'text' as const,
                version: 1,
              },
            ],
            direction: 'ltr' as const,
            format: '' as const,
            indent: 0,
            type: 'paragraph' as const,
            version: 1,
          },
        ],
        direction: 'ltr' as const,
        format: '' as const,
        indent: 0,
        type: 'root' as const,
        version: 1,
      },
    })

    // First, let's check what's required by looking at existing data
    payload.logger.info('Checking existing partners...')
    const existingPartners = await payload.find({
      collection: 'partners',
      limit: 5,
    })
    payload.logger.info(`Found ${existingPartners.totalDocs} existing partners`)

    // Create a simple media item first
    payload.logger.info('Creating test media...')
    let logoId = null
    try {
      const media = await payload.create({
        collection: 'media',
        data: {
          alt: 'Test Partner Logo',
          filename: 'test-logo.jpg',
          mimeType: 'image/jpeg',
          filesize: 1000,
          width: 200,
          height: 100,
          // Use a simple data URL for testing
          url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjY2NjIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzMzMyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkxvZ288L3RleHQ+PC9zdmc+',
        },
      })
      logoId = media.id
      payload.logger.info(`Created media with ID: ${logoId}`)
    } catch (error) {
      payload.logger.error('Failed to create media:', error)
    }

    // Try to create a minimal partner
    payload.logger.info('Creating test partner...')
    const testPartner = {
      name: 'Test Partner Organization',
      summary: 'A test partner for validation purposes.',
      description: createRichText('This is a test partner created to validate the seeding process and ensure all required fields are properly configured.'),
      logo: logoId,
      type: 'private',
      category: 'test',
      status: 'active',
      featured: false,
      verified: false,
    }

    payload.logger.info('Partner data:', JSON.stringify(testPartner, null, 2))

    const result = await payload.create({
      collection: 'partners',
      data: testPartner,
    })

    payload.logger.info('✅ Successfully created test partner!')
    payload.logger.info('Partner ID:', result.id)
    payload.logger.info('Partner Name:', result.name)

    // Verify it was created by fetching it back
    const verification = await payload.find({
      collection: 'partners',
      limit: 1,
    })
    
    payload.logger.info(`Verification: Found ${verification.totalDocs} partners after creation`)

  } catch (error) {
    payload.logger.error('❌ Error creating test partner:', error)
    if (error.data) {
      payload.logger.error('Validation errors:', JSON.stringify(error.data, null, 2))
    }
    throw error
  }
}
