import type { CollectionAfter<PERSON>hangeHook } from 'payload'

export const setDatabaseMediaUrl: CollectionAfterChangeHook = async ({ doc, operation, req }) => {
  // Only process for create operations and when we have base64 data stored in database
  if (operation === 'create' && doc.storageType === 'database' && doc.base64Data) {
    try {
      // Set the URL to the database media endpoint
      const updatedDoc = await req.payload.update({
        collection: 'media',
        id: doc.id,
        data: {
          url: `/api/media/database/${doc.id}`,
        },
      })
      
      req.payload.logger.info(`Set database media URL for ${doc.id}: /api/media/database/${doc.id}`)
      
      return updatedDoc
    } catch (error) {
      req.payload.logger.error('Error setting database media URL:', error)
      // Don't throw error, just log it - the document was created successfully
    }
  }
  
  return doc
}
