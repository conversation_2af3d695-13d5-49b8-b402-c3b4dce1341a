import type { CollectionConfig } from 'payload'
import { authenticated } from '../../access/authenticated'
import { anyone } from '../../access/anyone'
import { generateUniversalId, getIdFieldConfig } from '../../hooks/generateUniversalId'
import { sanitizeForDatabase } from '../../hooks/sanitizeForDatabase'
import { directImageUploadField } from '../../fields/imageUpload'

export const PageContent: CollectionConfig = {
  slug: 'page-content',
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'pageSlug', 'sectionKey', 'published'],
    group: 'Content Management',
  },
  labels: {
    singular: 'Page Content',
    plural: 'Page Content',
  },
  fields: [
    getIdFieldConfig('page-content'),
    {
      name: 'pageSlug',
      type: 'select',
      required: true,
      options: [
        { label: 'Home Page', value: 'home' },
        { label: 'About Page', value: 'about' },
        { label: 'Strategic Alignment', value: 'strategic-alignment' },
        { label: 'Operations Structure', value: 'operations-structure' },
        { label: 'Strategic Pillars', value: 'strategic-pillars' },
        { label: 'Projects', value: 'projects' },
        { label: 'Success Stories', value: 'success-stories' },
        { label: 'News', value: 'news' },
        { label: 'Events', value: 'events' },
        { label: 'Resources', value: 'resources' },
        { label: 'Partnerships', value: 'partnerships' },
        { label: 'Contact', value: 'contact' },
      ],
      admin: {
        description: 'Which page this content belongs to',
      },
    },
    {
      name: 'sectionKey',
      type: 'select',
      required: true,
      options: [
        { label: 'Hero Section', value: 'hero' },
        { label: 'Introduction', value: 'intro' },
        { label: 'Mission & Vision', value: 'mission-vision' },
        { label: 'History Timeline', value: 'history-timeline' },
        { label: 'Strategic Alignment', value: 'strategic-alignment' },
        { label: 'Operations Structure', value: 'operations-structure' },
        { label: 'Team Information', value: 'team-info' },
        { label: 'Statistics', value: 'statistics' },
        { label: 'Call to Action', value: 'cta' },
        { label: 'Footer Content', value: 'footer' },
      ],
      admin: {
        description: 'Which section of the page this content is for',
      },
    },
    {
      name: 'title',
      type: 'text',
      required: true,
      admin: {
        description: 'Main title for this content section',
      },
    },
    {
      name: 'subtitle',
      type: 'text',
      admin: {
        description: 'Optional subtitle or tagline',
      },
    },
    {
      name: 'content',
      type: 'richText',
      admin: {
        description: 'Main content with rich text formatting',
      },
    },
    {
      name: 'summary',
      type: 'textarea',
      maxLength: 500,
      admin: {
        description: 'Brief summary or description (max 500 characters)',
      },
    },
    directImageUploadField({
      name: 'backgroundImage',
      label: 'Background Image',
      required: false,
      admin: {
        description: 'Background image for hero sections or banners',
      },
    }),
    directImageUploadField({
      name: 'featuredImage',
      label: 'Featured Image',
      required: false,
      admin: {
        description: 'Main featured image for the content',
      },
    }),
    {
      name: 'ctaButtons',
      type: 'array',
      fields: [
        {
          name: 'text',
          type: 'text',
          required: true,
          admin: {
            description: 'Button text',
          },
        },
        {
          name: 'url',
          type: 'text',
          required: true,
          admin: {
            description: 'Button URL (internal or external)',
          },
        },
        {
          name: 'style',
          type: 'select',
          options: [
            { label: 'Primary', value: 'primary' },
            { label: 'Secondary', value: 'secondary' },
            { label: 'Outline', value: 'outline' },
            { label: 'Ghost', value: 'ghost' },
          ],
          defaultValue: 'primary',
        },
        {
          name: 'openInNewTab',
          type: 'checkbox',
          defaultValue: false,
        },
      ],
      admin: {
        description: 'Call-to-action buttons for this content',
      },
    },
    {
      name: 'metadata',
      type: 'group',
      fields: [
        {
          name: 'metaTitle',
          type: 'text',
          admin: {
            description: 'SEO meta title',
          },
        },
        {
          name: 'metaDescription',
          type: 'textarea',
          maxLength: 160,
          admin: {
            description: 'SEO meta description (max 160 characters)',
          },
        },
        {
          name: 'keywords',
          type: 'array',
          fields: [
            {
              name: 'keyword',
              type: 'text',
            },
          ],
          admin: {
            description: 'SEO keywords',
          },
        },
      ],
    },
    {
      name: 'displayOrder',
      type: 'number',
      defaultValue: 1,
      admin: {
        description: 'Order to display this content within the section',
      },
    },
    {
      name: 'published',
      type: 'checkbox',
      defaultValue: true,
      admin: {
        description: 'Make this content visible to the public',
      },
    },
    {
      name: 'lastUpdated',
      type: 'date',
      admin: {
        readOnly: true,
        description: 'Last time this content was updated',
      },
    },
  ],
  hooks: {
    beforeChange: [
      generateUniversalId,
      sanitizeForDatabase,
      ({ data }) => {
        // Auto-update lastUpdated timestamp
        data.lastUpdated = new Date()
        return data
      },
    ],
  },
}

export default PageContent
