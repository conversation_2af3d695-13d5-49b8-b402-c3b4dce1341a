import type { Payload } from 'payload'
import { seedComprehensiveCMSData } from './comprehensive-cms-data'

export const seedCMSData = async (payload: Payload): Promise<void> => {
  payload.logger.info('🌱 Starting comprehensive CMS data seeding...')

  try {
    // Use the comprehensive seed data function
    await seedComprehensiveCMSData(payload)
    
    payload.logger.info('🎉 CMS data seeding completed successfully!')
  } catch (error) {
    payload.logger.error('❌ Error during CMS data seeding:', error)
    throw error
  }
}
