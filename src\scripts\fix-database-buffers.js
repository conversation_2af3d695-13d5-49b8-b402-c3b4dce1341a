/**
 * <PERSON><PERSON><PERSON> to fix BSON Buffer serialization issues in the database
 * This script will find and fix any Buffer objects stored in the database
 */

import { config as dotenvConfig } from 'dotenv'
import { MongoClient } from 'mongodb'

// Load environment variables
dotenvConfig()

const DATABASE_URI = process.env.DATABASE_URI

if (!DATABASE_URI) {
  console.error('❌ DATABASE_URI environment variable is required')
  process.exit(1)
}

async function fixDatabaseBuffers() {
  let client
  
  try {
    console.log('🔧 Connecting to MongoDB...')
    client = new MongoClient(DATABASE_URI)
    await client.connect()
    
    const db = client.db()
    console.log('✅ Connected to database')
    
    // Collections that might have Buffer issues
    const collectionsToFix = [
      'media',
      'projects', 
      'success-stories',
      'news',
      'resources',
      'media-gallery'
    ]
    
    for (const collectionName of collectionsToFix) {
      console.log(`\n🔍 Checking collection: ${collectionName}`)
      
      const collection = db.collection(collectionName)
      const documents = await collection.find({}).toArray()
      
      console.log(`   Found ${documents.length} documents`)
      
      let fixedCount = 0
      
      for (const doc of documents) {
        let needsUpdate = false
        const updates = {}
        
        // Check for Buffer objects in common fields
        const fieldsToCheck = [
          'base64Data',
          'image',
          'featuredImage',
          'gallery',
          'data',
          'file'
        ]
        
        for (const field of fieldsToCheck) {
          if (doc[field]) {
            const fixed = fixBufferInField(doc[field], field)
            if (fixed.changed) {
              updates[field] = fixed.value
              needsUpdate = true
            }
          }
        }
        
        // Check nested objects
        if (doc.image && typeof doc.image === 'object') {
          const fixed = fixBufferInObject(doc.image)
          if (fixed.changed) {
            updates.image = fixed.value
            needsUpdate = true
          }
        }
        
        if (doc.gallery && Array.isArray(doc.gallery)) {
          const fixedGallery = doc.gallery.map(item => {
            if (typeof item === 'object') {
              const fixed = fixBufferInObject(item)
              return fixed.changed ? fixed.value : item
            }
            return item
          })
          
          if (JSON.stringify(fixedGallery) !== JSON.stringify(doc.gallery)) {
            updates.gallery = fixedGallery
            needsUpdate = true
          }
        }
        
        if (needsUpdate) {
          await collection.updateOne(
            { _id: doc._id },
            { $set: updates }
          )
          fixedCount++
          console.log(`   ✅ Fixed document ${doc._id}`)
        }
      }
      
      console.log(`   📊 Fixed ${fixedCount} documents in ${collectionName}`)
    }
    
    console.log('\n🎉 Database cleanup completed successfully!')
    
  } catch (error) {
    console.error('❌ Error fixing database:', error)
    process.exit(1)
  } finally {
    if (client) {
      await client.close()
      console.log('🔌 Database connection closed')
    }
  }
}

function fixBufferInField(value, fieldName) {
  if (Buffer.isBuffer(value)) {
    console.log(`   🔧 Converting Buffer in field '${fieldName}' to base64 string`)
    return {
      changed: true,
      value: value.toString('base64')
    }
  }
  
  if (Array.isArray(value)) {
    let changed = false
    const newArray = value.map(item => {
      if (Buffer.isBuffer(item)) {
        changed = true
        return item.toString('base64')
      }
      return item
    })
    
    return { changed, value: changed ? newArray : value }
  }
  
  if (typeof value === 'object' && value !== null) {
    return fixBufferInObject(value)
  }
  
  return { changed: false, value }
}

function fixBufferInObject(obj) {
  let changed = false
  const newObj = {}
  
  for (const [key, value] of Object.entries(obj)) {
    if (Buffer.isBuffer(value)) {
      console.log(`   🔧 Converting Buffer in object field '${key}' to base64 string`)
      newObj[key] = value.toString('base64')
      changed = true
    } else if (Array.isArray(value)) {
      const fixed = fixBufferInField(value, key)
      newObj[key] = fixed.value
      if (fixed.changed) changed = true
    } else if (typeof value === 'object' && value !== null) {
      const fixed = fixBufferInObject(value)
      newObj[key] = fixed.value
      if (fixed.changed) changed = true
    } else {
      newObj[key] = value
    }
  }
  
  return { changed, value: changed ? newObj : obj }
}

// Run the script
fixDatabaseBuffers().catch(console.error)
