import type { Payload } from 'payload'

export default async function seedNewCollections(payload: Payload) {
  payload.logger.info('🌱 Seeding new collections (Strategic Pillars, Page Content, Statistics)...')

  try {
    // Seed Strategic Pillars
    await seedStrategicPillars(payload)
    
    // Seed Page Content
    await seedPageContent(payload)
    
    // Seed Statistics
    await seedStatistics(payload)

    payload.logger.info('✅ New collections seeded successfully!')
  } catch (error) {
    payload.logger.error('❌ Error seeding new collections:', error)
    throw error
  }
}

async function seedStrategicPillars(payload: Payload) {
  payload.logger.info('📊 Seeding Strategic Pillars...')

  const pillars = [
    {
      pillarId: 'knowledge-documentation',
      title: 'Indigenous Knowledge Documentation',
      shortTitle: 'Knowledge Documentation',
      description: {
        root: {
          type: 'root',
          children: [
            {
              type: 'paragraph',
              children: [
                {
                  type: 'text',
                  text: 'Systematic collection, preservation, and digitization of traditional knowledge systems across Kenya\'s 47 counties.',
                },
              ],
            },
          ],
        },
      },
      summary: 'Systematic collection, preservation, and digitization of traditional knowledge systems across Kenya\'s 47 counties.',
      icon: 'database',
      color: {
        primary: '#8A3E25',
        secondary: '#725242',
      },
      objectives: [
        { objective: 'Document indigenous knowledge from all 47 counties' },
        { objective: 'Establish standardized documentation protocols' },
        { objective: 'Create comprehensive digital knowledge repository' },
        { objective: 'Ensure community ownership and benefit-sharing' },
      ],
      keyMetrics: {
        targetValue: 47,
        currentValue: 25,
        unit: 'counties',
        description: 'Counties with documented indigenous knowledge',
      },
      milestones: [
        {
          title: 'Documentation Framework Established',
          description: 'Standardized protocols for knowledge collection',
          status: 'completed',
          targetDate: '2023-03-01',
          completedDate: '2023-02-15',
        },
        {
          title: 'Digital Repository Launch',
          description: 'IKIA database platform operational',
          status: 'completed',
          targetDate: '2023-06-01',
          completedDate: '2023-05-20',
        },
        {
          title: 'Community Training Programs',
          description: 'Train local knowledge keepers',
          status: 'in-progress',
          targetDate: '2024-03-01',
        },
      ],
      order: 1,
      published: true,
    },
    {
      pillarId: 'product-development',
      title: 'Product Development & Commercialization',
      shortTitle: 'Product Development',
      description: {
        root: {
          type: 'root',
          children: [
            {
              type: 'paragraph',
              children: [
                {
                  type: 'text',
                  text: 'Innovation-driven approach to developing marketable products from traditional knowledge and natural resources.',
                },
              ],
            },
          ],
        },
      },
      summary: 'Innovation-driven approach to developing marketable products from traditional knowledge and natural resources.',
      icon: 'lightbulb',
      color: {
        primary: '#25718A',
        secondary: '#725242',
      },
      objectives: [
        { objective: 'Develop innovative natural products' },
        { objective: 'Establish quality standards and certifications' },
        { objective: 'Create market-ready product lines' },
        { objective: 'Support community-based enterprises' },
      ],
      keyMetrics: {
        targetValue: 50,
        currentValue: 18,
        unit: 'products',
        description: 'Products developed and commercialized',
      },
      milestones: [
        {
          title: 'Research & Development Lab',
          description: 'Establish product development facilities',
          status: 'completed',
          targetDate: '2023-04-01',
          completedDate: '2023-03-28',
        },
        {
          title: 'First Product Launch',
          description: 'Launch first commercial products',
          status: 'in-progress',
          targetDate: '2024-02-01',
        },
      ],
      order: 2,
      published: true,
    },
    {
      pillarId: 'capacity-building',
      title: 'Capacity Building & Community Empowerment',
      shortTitle: 'Capacity Building',
      description: {
        root: {
          type: 'root',
          children: [
            {
              type: 'paragraph',
              children: [
                {
                  type: 'text',
                  text: 'Comprehensive training and empowerment programs to build local capacity and leadership.',
                },
              ],
            },
          ],
        },
      },
      summary: 'Comprehensive training and empowerment programs to build local capacity and leadership.',
      icon: 'users',
      color: {
        primary: '#8A3E25',
        secondary: '#25718A',
      },
      objectives: [
        { objective: 'Train community knowledge keepers' },
        { objective: 'Develop local leadership capacity' },
        { objective: 'Establish community-based organizations' },
        { objective: 'Create sustainable livelihood opportunities' },
      ],
      keyMetrics: {
        targetValue: 2000,
        currentValue: 850,
        unit: 'people',
        description: 'Community members trained',
      },
      milestones: [
        {
          title: 'Training Centers Established',
          description: 'Regional training facilities operational',
          status: 'completed',
          targetDate: '2023-07-01',
          completedDate: '2023-06-15',
        },
        {
          title: 'Community Leader Certification',
          description: 'Certified community leaders program',
          status: 'in-progress',
          targetDate: '2024-01-01',
        },
      ],
      order: 3,
      published: true,
    },
    {
      pillarId: 'ip-protection',
      title: 'Intellectual Property Protection',
      shortTitle: 'IP Protection',
      description: {
        root: {
          type: 'root',
          children: [
            {
              type: 'paragraph',
              children: [
                {
                  type: 'text',
                  text: 'Comprehensive framework for protecting traditional knowledge and ensuring community benefit-sharing.',
                },
              ],
            },
          ],
        },
      },
      summary: 'Comprehensive framework for protecting traditional knowledge and ensuring community benefit-sharing.',
      icon: 'shield',
      color: {
        primary: '#725242',
        secondary: '#8A3E25',
      },
      objectives: [
        { objective: 'Establish IP protection frameworks' },
        { objective: 'Register traditional knowledge patents' },
        { objective: 'Implement benefit-sharing mechanisms' },
        { objective: 'Provide legal support to communities' },
      ],
      keyMetrics: {
        targetValue: 25,
        currentValue: 8,
        unit: 'patents',
        description: 'Traditional knowledge patents registered',
      },
      milestones: [
        {
          title: 'IP Framework Development',
          description: 'Legal framework for IP protection',
          status: 'completed',
          targetDate: '2023-05-01',
          completedDate: '2023-04-20',
        },
        {
          title: 'First Patent Applications',
          description: 'Submit first traditional medicine patents',
          status: 'in-progress',
          targetDate: '2024-01-15',
        },
      ],
      order: 4,
      published: true,
    },
  ]

  for (const pillar of pillars) {
    try {
      await payload.create({
        collection: 'strategic-pillars',
        data: pillar,
      })
      payload.logger.info(`   ✓ Created pillar: ${pillar.title}`)
    } catch (error) {
      payload.logger.error(`   ✗ Failed to create pillar ${pillar.title}:`, error)
    }
  }
}

async function seedPageContent(payload: Payload) {
  payload.logger.info('📄 Seeding Page Content...')

  const pageContent = [
    {
      pageSlug: 'home',
      sectionKey: 'hero',
      title: 'Natural Products Industry Initiative',
      subtitle: 'Harnessing Indigenous Wealth for Sustainable Growth',
      summary: 'Transforming Kenya\'s rich traditional knowledge and natural resources into sustainable economic opportunities through community-driven innovation.',
      displayOrder: 1,
      published: true,
    },
    {
      pageSlug: 'about',
      sectionKey: 'hero',
      title: 'About NPI',
      subtitle: 'Transforming Traditional Knowledge into Economic Opportunities',
      summary: 'Learn about our mission, vision, and comprehensive approach to natural products development.',
      displayOrder: 1,
      published: true,
    },
    {
      pageSlug: 'strategic-pillars',
      sectionKey: 'hero',
      title: 'Strategic Pillars',
      subtitle: 'Four Interconnected Foundations for Success',
      summary: 'Explore the four strategic pillars that guide our comprehensive approach to natural products development.',
      displayOrder: 1,
      published: true,
    },
  ]

  for (const content of pageContent) {
    try {
      await payload.create({
        collection: 'page-content',
        data: content,
      })
      payload.logger.info(`   ✓ Created content: ${content.pageSlug}/${content.sectionKey}`)
    } catch (error) {
      payload.logger.error(`   ✗ Failed to create content ${content.pageSlug}/${content.sectionKey}:`, error)
    }
  }
}

async function seedStatistics(payload: Payload) {
  payload.logger.info('📈 Seeding Statistics...')

  const statistics = [
    {
      statKey: 'counties-covered',
      label: 'Counties Covered',
      value: 25,
      unit: '',
      description: 'Number of counties with active NPI programs',
      category: 'reach',
      displayFormat: 'number',
      trend: {
        direction: 'up',
        percentage: 25,
        period: 'vs last year',
      },
      displayOrder: 1,
      featured: true,
      published: true,
    },
    {
      statKey: 'projects-active',
      label: 'Active Projects',
      value: 42,
      unit: '',
      description: 'Currently active projects across all pillars',
      category: 'projects',
      displayFormat: 'number',
      trend: {
        direction: 'up',
        percentage: 15,
        period: 'vs last quarter',
      },
      displayOrder: 2,
      featured: true,
      published: true,
    },
    {
      statKey: 'community-members-trained',
      label: 'Community Members Trained',
      value: 850,
      unit: '',
      description: 'Total number of community members trained',
      category: 'capacity',
      displayFormat: 'number',
      trend: {
        direction: 'up',
        percentage: 40,
        period: 'vs last year',
      },
      displayOrder: 3,
      featured: true,
      published: true,
    },
    {
      statKey: 'success-stories',
      label: 'Success Stories',
      value: 28,
      unit: '',
      description: 'Documented success stories from communities',
      category: 'impact',
      displayFormat: 'number',
      trend: {
        direction: 'up',
        percentage: 30,
        period: 'vs last year',
      },
      displayOrder: 4,
      featured: true,
      published: true,
    },
  ]

  for (const stat of statistics) {
    try {
      await payload.create({
        collection: 'statistics',
        data: stat,
      })
      payload.logger.info(`   ✓ Created statistic: ${stat.label}`)
    } catch (error) {
      payload.logger.error(`   ✗ Failed to create statistic ${stat.label}:`, error)
    }
  }
}
