import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams
    const query = searchParams.get('q') || searchParams.get('query')
    const limit = parseInt(searchParams.get('limit') || '10')
    const collections = searchParams.get('collections')?.split(',') || [
      'projects', 'success-stories', 'news', 'resources', 'events'
    ]

    if (!query || query.trim().length < 2) {
      return NextResponse.json({
        success: true,
        results: [],
        message: 'Query must be at least 2 characters long'
      })
    }

    const searchResults: any[] = []

    // Search across different collections
    for (const collection of collections) {
      try {
        let where: any = {}
        let searchFields: string[] = []

        // Define search fields for each collection
        switch (collection) {
          case 'projects':
            where = {
              published: { equals: true },
              or: [
                { title: { contains: query } },
                { summary: { contains: query } },
                { 'tags.tag': { contains: query } }
              ]
            }
            searchFields = ['title', 'summary', 'slug']
            break

          case 'success-stories':
            where = {
              published: { equals: true },
              or: [
                { title: { contains: query } },
                { summary: { contains: query } },
                { 'tags.tag': { contains: query } }
              ]
            }
            searchFields = ['title', 'summary', 'slug']
            break

          case 'news':
            where = {
              status: { equals: 'published' },
              or: [
                { title: { contains: query } },
                { summary: { contains: query } },
                { 'tags.tag': { contains: query } }
              ]
            }
            searchFields = ['title', 'summary', 'slug']
            break

          case 'resources':
            where = {
              published: { equals: true },
              or: [
                { title: { contains: query } },
                { summary: { contains: query } },
                { 'keywords.keyword': { contains: query } }
              ]
            }
            searchFields = ['title', 'summary', 'slug']
            break

          case 'events':
            where = {
              published: { equals: true },
              or: [
                { title: { contains: query } },
                { summary: { contains: query } },
                { 'tags.tag': { contains: query } }
              ]
            }
            searchFields = ['title', 'summary', 'slug']
            break

          default:
            continue
        }

        const result = await payload.find({
          collection: collection as any,
          where,
          limit: Math.ceil(limit / collections.length), // Distribute limit across collections
          select: {
            id: true,
            title: true,
            summary: true,
            slug: true,
            createdAt: true,
            updatedAt: true,
            ...searchFields.reduce((acc, field) => ({ ...acc, [field]: true }), {})
          },
          sort: ['-updatedAt']
        })

        // Transform results to unified format
        const transformedResults = result.docs.map((doc: any) => ({
          id: doc.id,
          title: doc.title,
          description: doc.summary || doc.description || '',
          url: `/${collection}/${doc.slug || doc.id}`,
          type: collection.replace('-', ' '),
          collection,
          updatedAt: doc.updatedAt
        }))

        searchResults.push(...transformedResults)
      } catch (error) {
        console.error(`Error searching ${collection}:`, error)
        // Continue with other collections even if one fails
      }
    }

    // Sort all results by relevance/date and limit
    const sortedResults = searchResults
      .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
      .slice(0, limit)

    return NextResponse.json({
      success: true,
      results: sortedResults,
      query,
      total: sortedResults.length,
      collections: collections
    })

  } catch (error) {
    console.error('Search API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Failed to perform search'
      },
      { status: 500 }
    )
  }
}
