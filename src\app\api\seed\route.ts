import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

export async function POST(request: NextRequest) {
  try {
    // Check if we're in development mode for security
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json(
        {
          success: false,
          error: 'Seeding is only allowed in development mode'
        },
        { status: 403 }
      )
    }

    const payload = await getPayload({ config })

    // Import the seeding function dynamically to avoid import issues
    const { seedMinimalData } = await import('@/lib/seed/minimal-seed')

    // Run the minimal seeding for testing
    await seedMinimalData(payload)

    return NextResponse.json({
      success: true,
      message: 'CMS data seeding completed successfully!'
    })

  } catch (error) {
    console.error('Error during seeding:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to seed CMS data',
        details: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Use POST method to trigger seeding',
    note: 'Seeding is only available in development mode'
  })
}
