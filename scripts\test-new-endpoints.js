// Test script for new API endpoints
const BASE_URL = process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:3000'

async function testEndpoint(endpoint, description) {
  console.log(`\n🧪 Testing ${description}...`)
  console.log(`   URL: ${BASE_URL}${endpoint}`)
  
  try {
    const response = await fetch(`${BASE_URL}${endpoint}`)
    const data = await response.json()
    
    if (response.ok) {
      console.log(`   ✅ Success: ${response.status}`)
      console.log(`   📊 Data keys: ${Object.keys(data).join(', ')}`)
      
      if (data.success) {
        // Log collection-specific info
        if (data.pillars) {
          console.log(`   📋 Pillars found: ${data.pillars.length}`)
        } else if (data.content) {
          console.log(`   📄 Content items found: ${data.content.length}`)
        } else if (data.statistics) {
          console.log(`   📈 Statistics found: ${data.statistics.length}`)
        }
      }
    } else {
      console.log(`   ❌ Error: ${response.status}`)
      console.log(`   📝 Message: ${data.message || data.error || 'Unknown error'}`)
    }
  } catch (error) {
    console.log(`   💥 Request failed: ${error.message}`)
  }
}

async function testAllEndpoints() {
  console.log('🚀 Testing New API Endpoints')
  console.log('=' .repeat(50))
  
  // Test Strategic Pillars endpoints
  await testEndpoint('/api/strategic-pillars', 'Strategic Pillars List')
  await testEndpoint('/api/strategic-pillars?published=true', 'Published Strategic Pillars')
  
  // Test Page Content endpoints
  await testEndpoint('/api/page-content', 'Page Content List')
  await testEndpoint('/api/page-content?pageSlug=home', 'Home Page Content')
  await testEndpoint('/api/page-content?pageSlug=home&sectionKey=hero', 'Home Hero Content')
  
  // Test Statistics endpoints
  await testEndpoint('/api/statistics', 'Statistics List')
  await testEndpoint('/api/statistics?featured=true', 'Featured Statistics')
  await testEndpoint('/api/statistics?category=impact', 'Impact Statistics')
  
  // Test existing endpoints to ensure they still work
  console.log('\n🔄 Testing Existing Endpoints (Regression Test)')
  console.log('-' .repeat(50))
  
  await testEndpoint('/api/projects?limit=5', 'Projects (Sample)')
  await testEndpoint('/api/success-stories?limit=3', 'Success Stories (Sample)')
  await testEndpoint('/api/news?limit=3', 'News (Sample)')
  
  console.log('\n✅ API Endpoint Testing Complete!')
}

// Run the tests
testAllEndpoints().catch(console.error)
