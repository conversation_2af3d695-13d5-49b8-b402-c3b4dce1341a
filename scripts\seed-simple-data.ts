import type { Payload } from 'payload'

export default async function seedSimpleData(payload: Payload) {
  payload.logger.info('🌱 Seeding simple comprehensive data...')
  
  try {
    // Helper function to create rich text content
    const createRichText = (text: string) => ({
      root: {
        children: [
          {
            children: [
              {
                detail: 0,
                format: 0,
                mode: 'normal' as const,
                style: '',
                text,
                type: 'text' as const,
                version: 1,
              },
            ],
            direction: 'ltr' as const,
            format: '' as const,
            indent: 0,
            type: 'paragraph' as const,
            version: 1,
          },
        ],
        direction: 'ltr' as const,
        format: '' as const,
        indent: 0,
        type: 'root' as const,
        version: 1,
      },
    })

    // Create counties first
    payload.logger.info('Creating counties...')
    const counties = []
    
    const countyData = [
      { name: 'Nairobi', code: 'KE-047', lat: -1.2921, lng: 36.8219, desc: 'Capital city of Kenya' },
      { name: 'Kiam<PERSON>', code: 'KE-022', lat: -1.1719, lng: 36.8356, desc: 'Agricultural hub with coffee and tea farming' },
      { name: '<PERSON><PERSON><PERSON>', code: 'KE-017', lat: -1.5177, lng: 37.2634, desc: 'Home to traditional Kamba crafts' },
      { name: 'Turkana', code: 'KE-023', lat: 3.1167, lng: 35.5833, desc: 'Arid region with drought-resistant plants' },
    ]

    for (const county of countyData) {
      const created = await payload.create({
        collection: 'counties',
        data: {
          name: county.name,
          code: county.code,
          coordinates: { latitude: county.lat, longitude: county.lng },
          description: county.desc,
          isActive: true,
        },
      })
      counties.push(created)
      payload.logger.info(`✅ Created county: ${county.name}`)
    }

    // Create projects
    payload.logger.info('Creating projects...')
    const projects = [
      {
        title: 'Indigenous Knowledge Documentation Initiative',
        summary: 'Documenting and preserving traditional knowledge systems of indigenous communities across Kenya.',
        category: 'knowledge-preservation' as const,
        pillar: 'indigenous-knowledge' as const,
        status: 'active' as const,
        county: counties[0].id,
      },
      {
        title: 'Community-Led Natural Products Development',
        summary: 'Empowering communities to develop sustainable natural products businesses.',
        category: 'community-empowerment' as const,
        pillar: 'community-innovation' as const,
        status: 'active' as const,
        county: counties[1].id,
      },
      {
        title: 'Natural Products Research Hub',
        summary: 'Establishing a research and innovation hub for natural products development.',
        category: 'research-development' as const,
        pillar: 'capacity-building' as const,
        status: 'planning' as const,
        county: counties[1].id,
      },
      {
        title: 'Market Access Development',
        summary: 'Developing value chains to connect producers with markets.',
        category: 'market-development' as const,
        pillar: 'market-development' as const,
        status: 'active' as const,
        county: counties[2].id,
      },
      {
        title: 'Digital Knowledge Platform',
        summary: 'Creating a digital platform for traditional knowledge exchange.',
        category: 'technology-innovation' as const,
        pillar: 'technology-innovation' as const,
        status: 'active' as const,
        county: counties[3].id,
      },
    ]

    for (const project of projects) {
      const created = await payload.create({
        collection: 'projects',
        data: {
          title: project.title,
          description: createRichText(`${project.summary} This project aims to create lasting impact in communities across Kenya.`),
          summary: project.summary,
          category: project.category,
          pillar: project.pillar,
          status: project.status,
          location: {
            counties: [project.county],
            specificLocation: `${counties.find(c => c.id === project.county)?.name} County`,
            coordinates: { latitude: -1.0, longitude: 36.0 },
          },
          timeline: {
            startDate: '2024-01-01',
            endDate: '2025-12-31',
            duration: '2 years',
          },
          budget: {
            totalBudget: 10000000,
            currency: 'KES',
            fundingSources: [{ source: 'Government', amount: 10000000, percentage: 100 }],
          },
          impact: {
            beneficiaries: 1000,
            communities: 10,
            jobsCreated: 50,
          },
          team: { projectManager: 'Project Manager' },
          featured: true,
          published: true,
          tags: [{ tag: 'natural products' }],
          slug: project.title.toLowerCase().replace(/\s+/g, '-'),
        },
      })
      payload.logger.info(`✅ Created project: ${project.title}`)
    }

    // Verify data
    const projectCount = await payload.find({ collection: 'projects', limit: 1 })
    const countyCount = await payload.find({ collection: 'counties', limit: 1 })

    payload.logger.info(`📊 Created:`)
    payload.logger.info(`   Counties: ${countyCount.totalDocs}`)
    payload.logger.info(`   Projects: ${projectCount.totalDocs}`)
    payload.logger.info('✅ Simple seeding completed!')

  } catch (error) {
    payload.logger.error('❌ Error in simple seeding:', error)
    throw error
  }
}
