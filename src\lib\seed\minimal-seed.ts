import type { Payload } from 'payload'

export const seedMinimalData = async (payload: Payload): Promise<void> => {
  payload.logger.info('🌱 Starting minimal CMS data seeding...')

  try {
    // Check database connection first
    payload.logger.info('🔍 Checking database connection...')

    // Check if data already exists
    payload.logger.info('🔍 Checking for existing data...')
    const existingCounties = await payload.find({
      collection: 'counties',
      limit: 1,
      pagination: false,
    })

    payload.logger.info(`📊 Found ${existingCounties.docs.length} existing counties`)

    if (existingCounties.docs.length > 0) {
      payload.logger.info('⚠️  Data already exists. Clearing existing data first...')

      // Clear existing data for fresh seeding
      try {
        await payload.delete({ collection: 'counties', where: {} })
        await payload.delete({ collection: 'projects', where: {} })
        await payload.delete({ collection: 'success-stories', where: {} })
        await payload.delete({ collection: 'events', where: {} })
        await payload.delete({ collection: 'media', where: {} })
      } catch (clearError) {
        payload.logger.warn('⚠️  Could not clear some existing data:', clearError)
      }

      payload.logger.info('🗑️  Cleared existing data')
    }

    // Create a simple placeholder image
    const placeholderImageBuffer = Buffer.from(
      'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
      'base64'
    )

    // Create one media item
    payload.logger.info('📸 Creating media item...')
    const heroImage = await payload.create({
      collection: 'media',
      data: {
        filename: 'hero-image.png',
        mimeType: 'image/png',
        alt: 'Natural products research',
        category: 'general',
        description: 'Hero image for testing',
      },
      file: {
        data: placeholderImageBuffer,
        mimetype: 'image/png',
        name: 'hero-image.png',
        size: placeholderImageBuffer.length,
      },
    })

    payload.logger.info(`✅ Created media item with ID: ${heroImage.id}`)

    // Create a few counties
    const counties = [
      {
        name: 'Baringo',
        code: 'KE-030',
        coordinates: { latitude: 0.4684, longitude: 35.9737 },
        description: 'Known for its rich biodiversity and traditional aloe vera cultivation.',
        isActive: true,
      },
      {
        name: 'Kiambu',
        code: 'KE-022',
        coordinates: { latitude: -1.1719, longitude: 36.8356 },
        description: 'Agricultural hub with strong coffee and tea farming traditions.',
        isActive: true,
      },
    ]

    payload.logger.info('🏞️  Creating counties...')
    const createdCounties = []
    for (const county of counties) {
      payload.logger.info(`Creating county: ${county.name}`)
      const created = await payload.create({
        collection: 'counties',
        data: county,
      })
      createdCounties.push(created)
      payload.logger.info(`✅ Created county: ${county.name} with ID: ${created.id}`)
    }

    payload.logger.info(`✅ Created ${createdCounties.length} counties total`)

    // Create one project
    payload.logger.info('🚀 Creating project...')
    const project = await payload.create({
      collection: 'projects',
      data: {
        title: 'Indigenous Knowledge Documentation Initiative',
        description: {
          root: {
            children: [
              {
                children: [
                  {
                    detail: 0,
                    format: 0,
                    mode: 'normal',
                    style: '',
                    text: 'A comprehensive project to document and preserve traditional knowledge systems.',
                    type: 'text',
                    version: 1,
                  },
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                type: 'paragraph',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        },
        summary: 'Documenting traditional knowledge systems of indigenous communities.',
        image: {
          image: heroImage.id,
          alt: 'Indigenous Knowledge Documentation',
          caption: 'Community elders sharing knowledge',
        },
        category: 'knowledge-preservation',
        pillar: 'indigenous-knowledge',
        status: 'active',
        location: {
          counties: [createdCounties[0].id],
          specificLocation: 'Baringo County',
          coordinates: {
            latitude: 0.4684,
            longitude: 35.9737,
          },
        },
        timeline: {
          startDate: '2023-01-01',
          endDate: '2025-12-31',
          duration: '3 years',
        },
        budget: {
          totalBudget: 500000,
          currency: 'KES',
          fundingStatus: 'fully-funded',
        },
        impact: {
          beneficiaries: 1000,
          communities: 5,
          description: 'Preserving traditional knowledge for future generations',
        },
        featured: true,
        urgent: false,
        tags: [
          { tag: 'traditional-knowledge' },
          { tag: 'community-engagement' },
        ],
      },
    })

    payload.logger.info(`✅ Created project with ID: ${project.id}`)

    // Create one success story
    payload.logger.info('🌟 Creating success story...')
    const successStory = await payload.create({
      collection: 'success-stories',
      data: {
        title: 'Aloe Vera Cooperative Success',
        summary: 'Local cooperative increases income through sustainable aloe vera processing.',
        content: {
          root: {
            children: [
              {
                children: [
                  {
                    detail: 0,
                    format: 0,
                    mode: 'normal',
                    style: '',
                    text: 'The Chepkemoi Women\'s Cooperative has successfully transformed their community through sustainable aloe vera processing.',
                    type: 'text',
                    version: 1,
                  },
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                type: 'paragraph',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        },
        image: {
          image: heroImage.id,
          alt: 'Aloe Vera Processing',
          caption: 'Women processing aloe vera products',
        },
        category: 'economic-empowerment',
        location: {
          county: createdCounties[0].id,
          specificLocation: 'Baringo County',
          coordinates: {
            latitude: 0.4684,
            longitude: 35.9737,
          },
        },
        participants: {
          beneficiary: {
            name: 'Mary Chepkemoi',
            role: 'Cooperative Leader',
            organization: 'Chepkemoi Women\'s Cooperative',
          },
          knowledgeHolder: {
            name: 'Elder Sarah Kiplagat',
            title: 'Traditional Healer',
            expertise: 'Traditional aloe vera processing methods',
          },
        },
        impact: {
          metrics: [
            {
              metric: 'Household Income Increase',
              value: '300',
              unit: 'percent',
              description: 'Average increase in household income',
            },
            {
              metric: 'Jobs Created',
              value: '150',
              unit: 'jobs',
              description: 'New employment opportunities for women',
            },
          ],
          beneficiaries: 150,
          economicImpact: 'Increased household income by 300%',
          socialImpact: 'Created 150 jobs for women',
          environmentalImpact: 'Sustainable harvesting practices implemented',
        },
        testimonials: [
          {
            quote: 'This cooperative has transformed our lives. We now have sustainable income and our children can go to school.',
            author: 'Mary Chepkemoi',
            role: 'Cooperative Leader',
            organization: 'Chepkemoi Women\'s Cooperative',
          },
        ],
        timeline: {
          startDate: '2023-01-01',
          completionDate: '2024-01-15',
          duration: '12 months',
        },
        investment: {
          totalAmount: 500000,
          currency: 'KES',
          sources: [
            {
              source: 'Community Savings',
              amount: 200000,
              percentage: 40,
            },
            {
              source: 'Government Grant',
              amount: 300000,
              percentage: 60,
            },
          ],
        },
        featured: true,
        publishDate: '2024-01-15',
        status: 'published',
      },
    })

    payload.logger.info(`✅ Created success story with ID: ${successStory.id}`)

    // Create one event
    payload.logger.info('📅 Creating event...')
    const event = await payload.create({
      collection: 'events',
      data: {
        title: 'Natural Products Innovation Conference 2024',
        description: {
          root: {
            children: [
              {
                children: [
                  {
                    detail: 0,
                    format: 0,
                    mode: 'normal',
                    style: '',
                    text: 'Annual conference bringing together researchers, communities, and industry partners.',
                    type: 'text',
                    version: 1,
                  },
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                type: 'paragraph',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        },
        summary: 'Annual conference on natural products innovation and traditional knowledge.',
        image: {
          image: heroImage.id,
          alt: 'Conference participants',
          caption: 'Participants at the innovation conference',
        },
        type: 'conference',
        category: 'knowledge-sharing',
        status: 'upcoming',
        schedule: {
          startDate: '2024-09-15',
          endDate: '2024-09-17',
          startTime: '09:00',
          endTime: '17:00',
        },
        location: {
          venue: 'Kenyatta International Conference Centre',
          address: 'Harambee Avenue, Nairobi',
          county: createdCounties[1].id,
          coordinates: {
            latitude: -1.2921,
            longitude: 36.8219,
          },
        },
        registration: {
          required: true,
          fee: 5000,
          currency: 'KES',
          capacity: 500,
          deadline: '2024-09-01',
        },
        featured: true,
      },
    })

    payload.logger.info(`✅ Created event with ID: ${event.id}`)

    // Verify data was created
    payload.logger.info('🔍 Verifying created data...')
    const finalCounties = await payload.find({ collection: 'counties', limit: 10, pagination: false })
    const finalProjects = await payload.find({ collection: 'projects', limit: 10, pagination: false })
    const finalMedia = await payload.find({ collection: 'media', limit: 10, pagination: false })

    payload.logger.info(`📊 Final verification:`)
    payload.logger.info(`   - Counties: ${finalCounties.docs.length}`)
    payload.logger.info(`   - Projects: ${finalProjects.docs.length}`)
    payload.logger.info(`   - Media: ${finalMedia.docs.length}`)

    // Now seed Partners and Media Gallery with correct structure
    payload.logger.info('🤝 Seeding Partners...')

    const partnersData = [
      {
        name: 'Kenya Vision 2030',
        summary: "Kenya's development blueprint for transforming the country into a middle-income economy.",
        description: {
          root: {
            type: 'root',
            children: [
              {
                type: 'paragraph',
                children: [
                  {
                    type: 'text',
                    text: "Kenya's development blueprint for transforming the country into a middle-income economy through sustainable development initiatives.",
                  },
                ],
              },
            ],
          },
        },
        logo: heroImage.id,
        type: 'government',
        category: ['strategic'],
        status: 'active',
        contact: {
          website: 'https://vision2030.go.ke',
          primaryContact: {
            name: 'Vision 2030 Secretariat',
            role: 'Partnership Coordinator',
            email: '<EMAIL>',
          },
        },
        featured: true,
        verified: true,
      },
      {
        name: 'University of Nairobi',
        summary: 'Leading research institution partnering in natural products research and development.',
        description: {
          root: {
            type: 'root',
            children: [
              {
                type: 'paragraph',
                children: [
                  {
                    type: 'text',
                    text: 'Leading research institution partnering in natural products research and development, providing scientific validation and research support.',
                  },
                ],
              },
            ],
          },
        },
        logo: heroImage.id,
        type: 'academic',
        category: ['technical'],
        status: 'active',
        contact: {
          website: 'https://uonbi.ac.ke',
          primaryContact: {
            name: 'UoN Research Office',
            role: 'Research Coordinator',
            email: '<EMAIL>',
          },
        },
        featured: false,
        verified: true,
      },
    ]

    for (const partner of partnersData) {
      try {
        const created = await payload.create({
          collection: 'partners',
          data: partner,
        })
        payload.logger.info(`✅ Created partner: ${partner.name} (ID: ${created.id})`)
      } catch (error) {
        payload.logger.error(`❌ Failed to create partner ${partner.name}:`, error)
      }
    }

    // Seed Media Gallery
    payload.logger.info('📸 Seeding Media Gallery...')

    const mediaItems = [
      {
        title: 'NPI Documentary: Transforming Communities',
        description: {
          root: {
            type: 'root',
            children: [
              {
                type: 'paragraph',
                children: [
                  {
                    type: 'text',
                    text: 'Comprehensive documentary showcasing how NPI initiatives are transforming communities across Kenya through natural products development.',
                  },
                ],
              },
            ],
          },
        },
        caption: 'Documentary showcasing community transformation',
        type: 'video',
        media: heroImage.id,
        category: 'community-stories',
        dateCreated: new Date('2024-01-15').toISOString(),
        featured: true,
        published: true,
        technical: {
          duration: '12:45',
          format: 'MP4',
          quality: 'high'
        }
      },
      {
        title: 'Traditional Healers Share Their Wisdom',
        description: {
          root: {
            type: 'root',
            children: [
              {
                type: 'paragraph',
                children: [
                  {
                    type: 'text',
                    text: 'Interviews with traditional healers from different communities about their knowledge and practices.',
                  },
                ],
              },
            ],
          },
        },
        caption: 'Traditional healers sharing wisdom',
        type: 'video',
        media: heroImage.id,
        category: 'cultural-heritage',
        dateCreated: new Date('2023-10-15').toISOString(),
        featured: false,
        published: true,
        technical: {
          duration: '18:45',
          format: 'MP4',
          quality: 'high'
        }
      },
    ]

    for (const mediaItem of mediaItems) {
      try {
        const created = await payload.create({
          collection: 'media-gallery',
          data: mediaItem,
        })
        payload.logger.info(`✅ Created media item: ${mediaItem.title} (ID: ${created.id})`)
      } catch (error) {
        payload.logger.error(`❌ Failed to create media item ${mediaItem.title}:`, error)
      }
    }

    // Seed additional collections
    payload.logger.info('📰 Seeding News...')

    const newsItems = [
      {
        title: 'NPI Launches New Traditional Medicine Research Initiative',
        summary: 'A groundbreaking research program to validate traditional healing practices through modern scientific methods.',
        content: {
          root: {
            type: 'root',
            children: [
              {
                type: 'paragraph',
                children: [
                  {
                    type: 'text',
                    text: 'The Natural Products Institute has launched a comprehensive research initiative to bridge traditional healing practices with modern scientific validation. This program will work directly with traditional healers across Kenya to document, study, and validate indigenous medicinal knowledge.',
                  },
                ],
              },
            ],
          },
        },
        featuredImage: heroImage.id,
        category: 'research',
        publishedDate: new Date('2024-01-15').toISOString(),
        featured: true,
        published: true,
        tags: [
          { tag: 'research' },
          { tag: 'traditional medicine' },
          { tag: 'innovation' }
        ]
      },
      {
        title: 'Community Workshop Series Begins in Baringo County',
        summary: 'First in a series of community workshops focusing on sustainable natural products development.',
        content: {
          root: {
            type: 'root',
            children: [
              {
                type: 'paragraph',
                children: [
                  {
                    type: 'text',
                    text: 'NPI has begun conducting community workshops in Baringo County, focusing on sustainable harvesting practices and value addition for natural products. These workshops are part of our commitment to community-driven development.',
                  },
                ],
              },
            ],
          },
        },
        featuredImage: heroImage.id,
        category: 'community',
        publishedDate: new Date('2023-12-20').toISOString(),
        featured: false,
        published: true,
        tags: [
          { tag: 'community' },
          { tag: 'workshops' },
          { tag: 'baringo' }
        ]
      },
    ]

    for (const newsItem of newsItems) {
      try {
        const created = await payload.create({
          collection: 'news',
          data: newsItem,
        })
        payload.logger.info(`✅ Created news item: ${newsItem.title} (ID: ${created.id})`)
      } catch (error) {
        payload.logger.error(`❌ Failed to create news item ${newsItem.title}:`, error)
      }
    }

    // Seed Resources
    payload.logger.info('📚 Seeding Resources...')

    const resources = [
      {
        title: 'Traditional Medicine Handbook',
        description: {
          root: {
            type: 'root',
            children: [
              {
                type: 'paragraph',
                children: [
                  {
                    type: 'text',
                    text: 'Comprehensive guide to traditional medicinal plants found in Kenya, including preparation methods and safety guidelines.',
                  },
                ],
              },
            ],
          },
        },
        summary: 'Comprehensive guide to traditional medicinal plants in Kenya',
        type: 'guide',
        category: 'traditional-medicine',
        file: heroImage.id,
        downloadCount: 0,
        featured: true,
        published: true,
        tags: [
          { tag: 'traditional medicine' },
          { tag: 'plants' },
          { tag: 'guide' }
        ]
      },
      {
        title: 'Sustainable Harvesting Practices Manual',
        description: {
          root: {
            type: 'root',
            children: [
              {
                type: 'paragraph',
                children: [
                  {
                    type: 'text',
                    text: 'Best practices for sustainable harvesting of natural products to ensure long-term availability and ecosystem health.',
                  },
                ],
              },
            ],
          },
        },
        summary: 'Best practices for sustainable harvesting of natural products',
        type: 'manual',
        category: 'sustainability',
        file: heroImage.id,
        downloadCount: 0,
        featured: false,
        published: true,
        tags: [
          { tag: 'sustainability' },
          { tag: 'harvesting' },
          { tag: 'conservation' }
        ]
      },
    ]

    for (const resource of resources) {
      try {
        const created = await payload.create({
          collection: 'resources',
          data: resource,
        })
        payload.logger.info(`✅ Created resource: ${resource.title} (ID: ${created.id})`)
      } catch (error) {
        payload.logger.error(`❌ Failed to create resource ${resource.title}:`, error)
      }
    }

    // Seed Events
    payload.logger.info('📅 Seeding Events...')

    const events = [
      {
        title: 'Traditional Medicine Research Symposium 2024',
        description: {
          root: {
            type: 'root',
            children: [
              {
                type: 'paragraph',
                children: [
                  {
                    type: 'text',
                    text: 'Join researchers, traditional healers, and policymakers for a comprehensive symposium on the future of traditional medicine research in Kenya.',
                  },
                ],
              },
            ],
          },
        },
        summary: 'Annual symposium bringing together researchers and traditional healers',
        featuredImage: heroImage.id,
        eventType: 'conference',
        category: 'research',
        startDate: new Date('2024-03-15T09:00:00Z').toISOString(),
        endDate: new Date('2024-03-17T17:00:00Z').toISOString(),
        location: {
          venue: 'University of Nairobi',
          address: 'University Way, Nairobi',
          city: 'Nairobi',
          county: createdCounties[1].id, // Nairobi
        },
        registration: {
          required: true,
          deadline: new Date('2024-03-01T23:59:59Z').toISOString(),
          fee: 5000,
          capacity: 200,
          currentRegistrations: 0,
        },
        featured: true,
        published: true,
        tags: [
          { tag: 'research' },
          { tag: 'traditional medicine' },
          { tag: 'symposium' }
        ]
      },
      {
        title: 'Community Workshop: Sustainable Harvesting Practices',
        description: {
          root: {
            type: 'root',
            children: [
              {
                type: 'paragraph',
                children: [
                  {
                    type: 'text',
                    text: 'Learn sustainable harvesting techniques for medicinal plants and natural products to ensure long-term availability and ecosystem health.',
                  },
                ],
              },
            ],
          },
        },
        summary: 'Hands-on workshop on sustainable harvesting techniques',
        featuredImage: heroImage.id,
        eventType: 'workshop',
        category: 'community',
        startDate: new Date('2024-02-20T10:00:00Z').toISOString(),
        endDate: new Date('2024-02-20T16:00:00Z').toISOString(),
        location: {
          venue: 'Baringo Community Center',
          address: 'Kabarnet Town',
          city: 'Kabarnet',
          county: createdCounties[0].id, // Baringo
        },
        registration: {
          required: true,
          deadline: new Date('2024-02-15T23:59:59Z').toISOString(),
          fee: 0,
          capacity: 50,
          currentRegistrations: 0,
        },
        featured: false,
        published: true,
        tags: [
          { tag: 'community' },
          { tag: 'sustainability' },
          { tag: 'workshop' }
        ]
      },
    ]

    for (const event of events) {
      try {
        const created = await payload.create({
          collection: 'events',
          data: event,
        })
        payload.logger.info(`✅ Created event: ${event.title} (ID: ${created.id})`)
      } catch (error) {
        payload.logger.error(`❌ Failed to create event ${event.title}:`, error)
      }
    }

    // Final verification
    payload.logger.info('📊 Final comprehensive verification:')
    const finalPartners = await payload.find({ collection: 'partners', limit: 0 })
    const finalMediaGallery = await payload.find({ collection: 'media-gallery', limit: 0 })
    const finalNews = await payload.find({ collection: 'news', limit: 0 })
    const finalResources = await payload.find({ collection: 'resources', limit: 0 })
    const finalEvents = await payload.find({ collection: 'events', limit: 0 })
    const finalSuccessStories = await payload.find({ collection: 'success-stories', limit: 0 })

    payload.logger.info(`   - Counties: ${finalCounties.docs.length}`)
    payload.logger.info(`   - Projects: ${finalProjects.docs.length}`)
    payload.logger.info(`   - Success Stories: ${finalSuccessStories.docs.length}`)
    payload.logger.info(`   - Partners: ${finalPartners.docs.length}`)
    payload.logger.info(`   - Media Gallery: ${finalMediaGallery.docs.length}`)
    payload.logger.info(`   - News: ${finalNews.docs.length}`)
    payload.logger.info(`   - Resources: ${finalResources.docs.length}`)
    payload.logger.info(`   - Events: ${finalEvents.docs.length}`)
    payload.logger.info(`   - Media: ${finalMedia.docs.length}`)

    payload.logger.info('🎉 Comprehensive CMS data seeding completed successfully!')

  } catch (error) {
    payload.logger.error('❌ Error during minimal seeding:', error)
    payload.logger.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    })
    throw error
  }
}
