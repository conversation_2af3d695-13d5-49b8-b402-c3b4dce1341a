import type { Payload } from 'payload'

export default async function forceSeed(payload: Payload) {
  payload.logger.info('🚀 Force seeding database with comprehensive data...')
  
  try {
    // Clear existing data first
    payload.logger.info('🧹 Clearing existing data...')
    
    const collections = [
      'projects',
      'success-stories', 
      'events',
      'news',
      'resources',
      'partners',
      'investment-opportunities',
      'media-gallery',
      'speakers',
      'partnerships',
      'contact-submissions',
      'partnership-applications'
    ]

    for (const collectionName of collections) {
      try {
        const existing = await payload.find({
          collection: collectionName as any,
          limit: 1000,
        })
        
        if (existing.docs.length > 0) {
          payload.logger.info(`   Deleting ${existing.docs.length} existing ${collectionName} entries...`)
          for (const doc of existing.docs) {
            await payload.delete({
              collection: collectionName as any,
              id: doc.id,
            })
          }
        }
      } catch (error) {
        payload.logger.warn(`   Could not clear ${collectionName}: ${error.message}`)
      }
    }

    // Create a simple test entry first
    payload.logger.info('🧪 Creating test county...')
    const testCounty = await payload.create({
      collection: 'counties',
      data: {
        name: 'Nairobi',
        code: 'KE-047',
        coordinates: {
          latitude: -1.2921,
          longitude: 36.8219,
        },
        description: 'Capital city of Kenya',
        isActive: true,
      },
    })
    payload.logger.info(`✅ Created test county: ${testCounty.id}`)

    // Create a simple test project
    payload.logger.info('🧪 Creating test project...')
    const testProject = await payload.create({
      collection: 'projects',
      data: {
        title: 'Test Natural Products Initiative',
        description: {
          root: {
            children: [
              {
                children: [
                  {
                    detail: 0,
                    format: 0,
                    mode: 'normal' as const,
                    style: '',
                    text: 'This is a test project to verify that seeding is working correctly.',
                    type: 'text' as const,
                    version: 1,
                  },
                ],
                direction: 'ltr' as const,
                format: '' as const,
                indent: 0,
                type: 'paragraph' as const,
                version: 1,
              },
            ],
            direction: 'ltr' as const,
            format: '' as const,
            indent: 0,
            type: 'root' as const,
            version: 1,
          },
        },
        summary: 'A test project to verify seeding functionality',
        category: 'research-development' as const,
        pillar: 'capacity-building' as const,
        status: 'active' as const,
        location: {
          counties: [testCounty.id],
          specificLocation: 'Nairobi, Kenya',
          coordinates: {
            latitude: -1.2921,
            longitude: 36.8219,
          },
        },
        timeline: {
          startDate: '2024-01-01',
          endDate: '2024-12-31',
          duration: '1 year',
        },
        budget: {
          totalBudget: 5000000,
          currency: 'KES',
          fundingSources: [
            {
              source: 'Government of Kenya',
              amount: 5000000,
              percentage: 100,
            },
          ],
        },
        impact: {
          beneficiaries: 1000,
          communities: 10,
          jobsCreated: 25,
        },
        team: {
          projectManager: 'Test Manager',
        },
        featured: true,
        published: true,
        tags: [
          { tag: 'test' },
          { tag: 'natural products' },
        ],
        slug: 'test-natural-products-initiative',
      },
    })
    payload.logger.info(`✅ Created test project: ${testProject.id}`)

    // Verify the data was created
    const projectCount = await payload.find({
      collection: 'projects',
      limit: 1,
    })
    
    const countyCount = await payload.find({
      collection: 'counties',
      limit: 1,
    })

    payload.logger.info(`📊 Verification:`)
    payload.logger.info(`   Projects: ${projectCount.totalDocs}`)
    payload.logger.info(`   Counties: ${countyCount.totalDocs}`)

    if (projectCount.totalDocs > 0 && countyCount.totalDocs > 0) {
      payload.logger.info('✅ Force seeding completed successfully!')
      payload.logger.info('🌐 You can now check the admin panel at http://localhost:3000/admin')
    } else {
      payload.logger.error('❌ Seeding verification failed - no data found')
    }
    
  } catch (error) {
    payload.logger.error('❌ Error in force seeding:', error)
    throw error
  }
}
