import type { Payload } from 'payload'

export default async function seedMissingCollections(payload: Payload) {
  payload.logger.info('🌱 Seeding missing collections...')
  
  try {
    // Helper function to create rich text content
    const createRichText = (text: string) => ({
      root: {
        children: [
          {
            children: [
              {
                detail: 0,
                format: 0,
                mode: 'normal' as const,
                style: '',
                text,
                type: 'text' as const,
                version: 1,
              },
            ],
            direction: 'ltr' as const,
            format: '' as const,
            indent: 0,
            type: 'paragraph' as const,
            version: 1,
          },
        ],
        direction: 'ltr' as const,
        format: '' as const,
        indent: 0,
        type: 'root' as const,
        version: 1,
      },
    })

    // First create some media for logos
    payload.logger.info('Creating media for partner logos...')
    const logoMedia = []

    const logoUrls = [
      'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=200&h=100&fit=crop',
      'https://images.unsplash.com/photo-1551434678-e076c223a692?w=200&h=100&fit=crop',
      'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=200&h=100&fit=crop',
      'https://images.unsplash.com/photo-1497366216548-37526070297c?w=200&h=100&fit=crop',
    ]

    for (let i = 0; i < logoUrls.length; i++) {
      try {
        const media = await payload.create({
          collection: 'media',
          data: {
            alt: `Partner Logo ${i + 1}`,
            filename: `partner-logo-${i + 1}.jpg`,
            mimeType: 'image/jpeg',
            filesize: 50000,
            width: 200,
            height: 100,
            url: logoUrls[i],
          },
        })
        logoMedia.push(media.id)
      } catch (error) {
        payload.logger.warn(`Failed to create logo media ${i + 1}:`, error)
        logoMedia.push(null)
      }
    }

    // Seed Partners
    payload.logger.info('Seeding Partners...')
    const partnersData = [
      {
        name: 'Kenya Vision 2030',
        summary: "Kenya's development blueprint for transforming the country into a middle-income economy.",
        description: createRichText("Kenya's development blueprint for transforming the country into a middle-income economy through sustainable development initiatives."),
        logo: logoMedia[0],
        type: 'government',
        category: 'government',
        status: 'active',
        contact: {
          website: 'https://vision2030.go.ke',
          primaryContact: {
            name: 'Vision 2030 Secretariat',
            email: '<EMAIL>',
          },
        },
        featured: true,
      },
      {
        name: 'National Museums of Kenya',
        summary: 'Leading institution in cultural heritage preservation and traditional knowledge documentation.',
        description: createRichText('Leading institution in cultural heritage preservation and traditional knowledge documentation, supporting NPI in preserving indigenous knowledge systems.'),
        logo: logoMedia[1],
        type: 'institution',
        category: 'cultural',
        status: 'active',
        contact: {
          website: 'https://museums.or.ke',
          primaryContact: {
            name: 'NMK Partnership Office',
            email: '<EMAIL>',
          },
        },
        featured: true,
      },
      {
        name: 'Kenya Association of Manufacturers',
        summary: 'Promoting manufacturing sector development and industrial growth in Kenya.',
        description: createRichText('Promoting manufacturing sector development and industrial growth in Kenya, supporting natural products commercialization and market access.'),
        logo: logoMedia[2],
        type: 'private',
        category: 'industry',
        status: 'active',
        contact: {
          website: 'https://kam.co.ke',
          primaryContact: {
            name: 'KAM Partnerships',
            email: '<EMAIL>',
          },
        },
        featured: false,
      },
      {
        name: 'University of Nairobi',
        summary: 'Leading research institution partnering in natural products research and development.',
        description: createRichText('Leading research institution partnering in natural products research and development, providing scientific validation and research support.'),
        logo: logoMedia[3],
        type: 'academic',
        category: 'research',
        status: 'active',
        contact: {
          website: 'https://uonbi.ac.ke',
          primaryContact: {
            name: 'UoN Research Office',
            email: '<EMAIL>',
          },
        },
        featured: false,
      },
    ]

    for (const partner of partnersData) {
      try {
        await payload.create({
          collection: 'partners',
          data: partner,
        })
      } catch (error) {
        payload.logger.error(`Failed to create partner ${partner.name}:`, error)
      }
    }

    // Seed Resources
    payload.logger.info('Seeding Resources...')
    const resourcesData = [
      {
        title: 'NPI Strategic Plan 2024-2028',
        summary: 'Comprehensive strategic framework for natural products development in Kenya.',
        description: createRichText('This strategic plan outlines NPI\'s vision, objectives, and implementation roadmap for advancing Kenya\'s natural products sector over the next five years.'),
        type: 'report',
        category: 'Strategic Planning',
        published: true,
        featured: true,
        publishDate: new Date('2024-01-15'),
        keywords: [
          { keyword: 'strategic planning' },
          { keyword: 'natural products' },
          { keyword: 'development' },
        ],
      },
      {
        title: 'Traditional Knowledge Documentation Guide',
        summary: 'Step-by-step guide for documenting indigenous knowledge while respecting community protocols.',
        description: createRichText('Comprehensive guide covering ethical protocols, documentation methods, and intellectual property considerations for traditional knowledge preservation.'),
        type: 'guide',
        category: 'Knowledge Preservation',
        published: true,
        featured: true,
        publishDate: new Date('2024-02-10'),
        keywords: [
          { keyword: 'traditional knowledge' },
          { keyword: 'documentation' },
          { keyword: 'community protocols' },
        ],
      },
      {
        title: 'Natural Products Market Analysis 2024',
        summary: 'Comprehensive analysis of Kenya\'s natural products market trends and opportunities.',
        description: createRichText('In-depth market research covering current trends, growth opportunities, and competitive landscape in Kenya\'s natural products sector.'),
        type: 'research',
        category: 'Market Intelligence',
        published: true,
        featured: false,
        publishDate: new Date('2024-03-05'),
        keywords: [
          { keyword: 'market analysis' },
          { keyword: 'trends' },
          { keyword: 'opportunities' },
        ],
      },
      {
        title: 'Community Enterprise Development Toolkit',
        summary: 'Practical toolkit for communities developing natural products enterprises.',
        description: createRichText('Complete toolkit including business planning templates, financial guides, and step-by-step instructions for community-based natural products enterprises.'),
        type: 'toolkit',
        category: 'Capacity Building',
        published: true,
        featured: false,
        publishDate: new Date('2024-03-20'),
        keywords: [
          { keyword: 'enterprise development' },
          { keyword: 'community' },
          { keyword: 'business planning' },
        ],
      },
    ]

    for (const resource of resourcesData) {
      await payload.create({
        collection: 'resources',
        data: resource,
      })
    }

    // Seed Media Gallery
    payload.logger.info('Seeding Media Gallery...')
    const mediaData = [
      {
        title: 'NPI Documentary: Transforming Communities',
        description: createRichText('Comprehensive documentary showcasing how NPI initiatives are transforming communities across Kenya through natural products development.'),
        type: 'video',
        category: 'Documentary',
        url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        duration: '12:45',
        views: 15420,
        featured: true,
        publishDate: new Date('2024-01-15'),
      },
      {
        title: 'Aloe Vera Cooperative Success Story',
        description: createRichText('Meet the women of Baringo County who transformed their community through traditional aloe vera knowledge.'),
        type: 'video',
        category: 'Success Stories',
        url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        duration: '8:30',
        views: 8750,
        featured: true,
        publishDate: new Date('2023-12-10'),
      },
      {
        title: 'Traditional Healers Share Their Wisdom',
        description: createRichText('Interviews with traditional healers from different communities about their knowledge and practices.'),
        type: 'video',
        category: 'Interviews',
        url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        duration: '18:45',
        views: 9800,
        featured: false,
        publishDate: new Date('2023-10-15'),
      },
      {
        title: 'Youth Entrepreneurs Leading Change',
        description: createRichText('Feature story on young entrepreneurs who are innovating in the natural products sector.'),
        type: 'video',
        category: 'Youth',
        url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        duration: '10:30',
        views: 6450,
        featured: false,
        publishDate: new Date('2023-09-20'),
      },
    ]

    for (const media of mediaData) {
      await payload.create({
        collection: 'media-gallery',
        data: media,
      })
    }

    // Seed Investment Opportunities
    payload.logger.info('Seeding Investment Opportunities...')
    const investmentData = [
      {
        title: 'Aloe Vera Processing Plant Expansion',
        summary: 'Investment opportunity to expand successful aloe vera processing operations in Baringo County.',
        description: createRichText('Proven business model with 300% ROI potential. Seeking investment to scale operations and reach regional markets.'),
        sector: 'Manufacturing',
        investmentType: 'Equity',
        status: 'active',
        featured: true,
        urgent: false,
        publishDate: new Date('2024-01-20'),
        funding: {
          targetAmount: 5000000,
          currency: 'KES',
          minimumInvestment: 500000,
        },
      },
      {
        title: 'Traditional Medicine Research Lab',
        summary: 'Establish state-of-the-art research facility for traditional medicine validation and development.',
        description: createRichText('Partnership opportunity to create world-class research facility for validating traditional medicines and developing new products.'),
        sector: 'Research & Development',
        investmentType: 'Partnership',
        status: 'active',
        featured: true,
        urgent: true,
        publishDate: new Date('2024-02-15'),
        funding: {
          targetAmount: 15000000,
          currency: 'KES',
          minimumInvestment: 1000000,
        },
      },
      {
        title: 'Community-Based Honey Production',
        summary: 'Scale sustainable honey production operations across multiple counties.',
        description: createRichText('Proven community-based model ready for scaling. Strong market demand and sustainable practices ensure long-term success.'),
        sector: 'Agriculture',
        investmentType: 'Loan',
        status: 'active',
        featured: false,
        urgent: false,
        publishDate: new Date('2024-03-10'),
        funding: {
          targetAmount: 3000000,
          currency: 'KES',
          minimumInvestment: 250000,
        },
      },
      {
        title: 'Natural Cosmetics Manufacturing',
        summary: 'Launch natural cosmetics line using indigenous plant extracts.',
        description: createRichText('Market-ready formulations using traditional plant knowledge. Export potential to regional and international markets.'),
        sector: 'Manufacturing',
        investmentType: 'Equity',
        status: 'active',
        featured: false,
        urgent: false,
        publishDate: new Date('2024-03-25'),
        funding: {
          targetAmount: 8000000,
          currency: 'KES',
          minimumInvestment: 750000,
        },
      },
    ]

    for (const investment of investmentData) {
      await payload.create({
        collection: 'investment-opportunities',
        data: investment,
      })
    }

    payload.logger.info('✅ Successfully seeded all missing collections!')
    payload.logger.info('📊 Seeded:')
    payload.logger.info('  • Partners: 4 entries')
    payload.logger.info('  • Resources: 4 entries')
    payload.logger.info('  • Media Gallery: 4 entries')
    payload.logger.info('  • Investment Opportunities: 4 entries')
    
  } catch (error) {
    payload.logger.error('❌ Error seeding missing collections:', error)
    throw error
  }
}
