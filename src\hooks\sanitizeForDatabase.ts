import type { CollectionBefore<PERSON>hangeHook } from 'payload'

/**
 * Hook to sanitize data before saving to database
 * Ensures no Buffer objects or other non-serializable data is stored
 */
export const sanitizeForDatabase: CollectionBeforeChangeHook = async ({ data, operation, req }) => {
  try {
    // Recursively clean the data object
    const cleanedData = sanitizeObject(data, req.payload.logger)

    // Log if we found and cleaned any problematic data
    if (JSON.stringify(cleanedData) !== JSON.stringify(data)) {
      req.payload.logger.info('Sanitized data before database save')
    }

    return cleanedData
  } catch (error) {
    req.payload.logger.error('Error sanitizing data for database:', error)
    return data
  }
}

/**
 * Recursively sanitize an object to remove non-serializable data
 */
function sanitizeObject(obj: any, logger?: any): any {
  if (obj === null || obj === undefined) {
    return obj
  }

  // Handle Buffer objects
  if (Buffer.isBuffer(obj)) {
    if (logger) {
      logger.warn('Found Buffer object in data, converting to base64 string')
    }
    return obj.toString('base64')
  }

  // Handle arrays
  if (Array.isArray(obj)) {
    return obj.map(item => sanitizeObject(item, logger))
  }

  // Handle objects
  if (typeof obj === 'object') {
    const sanitized: any = {}

    for (const [key, value] of Object.entries(obj)) {
      // Skip functions and undefined values
      if (typeof value === 'function' || value === undefined) {
        continue
      }

      // Handle Buffer objects
      if (Buffer.isBuffer(value)) {
        if (logger) {
          logger.warn(`Found Buffer in field '${key}', converting to base64 string`)
        }
        sanitized[key] = value.toString('base64')
        continue
      }

      // Recursively sanitize nested objects
      sanitized[key] = sanitizeObject(value, logger)
    }

    return sanitized
  }

  // Return primitive values as-is
  return obj
}
