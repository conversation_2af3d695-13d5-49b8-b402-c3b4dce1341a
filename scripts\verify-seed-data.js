#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to verify that the comprehensive seed data was properly created
 */

import { MongoClient } from 'mongodb'

async function verifySeededData() {
  console.log('🔍 Verifying seeded data...')

  try {
    // Connect to MongoDB
    const client = new MongoClient(process.env.DATABASE_URI || 'mongodb://localhost:27017/npi-website')
    await client.connect()
    console.log('✅ Connected to MongoDB')

    const db = client.db()

    // Check each collection
    const collections = [
      'projects',
      'success-stories',
      'events',
      'news',
      'resources',
      'partners',
      'investment-opportunities',
      'media-gallery',
      'speakers',
      'partnerships',
      'contact-submissions',
      'partnership-applications',
      'counties',
      'media'
    ]

    console.log('\n📊 Collection counts:')
    let totalEntries = 0

    for (const collectionName of collections) {
      try {
        const count = await db.collection(collectionName).countDocuments()
        console.log(`   • ${collectionName}: ${count} entries`)
        totalEntries += count
      } catch (error) {
        console.log(`   • ${collectionName}: Collection not found or error`)
      }
    }

    console.log(`\n📈 Total entries: ${totalEntries}`)

    // Check for specific data integrity
    console.log('\n🔗 Checking data relationships...')

    // Check if projects have proper county references
    const projectsWithCounties = await db.collection('projects').find({
      'location.counties': { $exists: true, $ne: [] }
    }).count()
    console.log(`   • Projects with county references: ${projectsWithCounties}`)

    // Check if success stories have proper categories
    const successStoriesWithCategories = await db.collection('success-stories').find({
      'category': { $exists: true, $ne: null }
    }).count()
    console.log(`   • Success stories with categories: ${successStoriesWithCategories}`)

    // Check if events have proper dates
    const eventsWithDates = await db.collection('events').find({
      'schedule.startDate': { $exists: true, $ne: null }
    }).count()
    console.log(`   • Events with start dates: ${eventsWithDates}`)

    // Check if media entries exist
    const mediaCount = await db.collection('media').countDocuments()
    console.log(`   • Media files: ${mediaCount}`)

    await client.close()
    console.log('\n✅ Data verification completed successfully!')

  } catch (error) {
    console.error('❌ Error verifying seeded data:', error)
    process.exit(1)
  }
}

// Run the verification
verifySeededData()
