import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const category = searchParams.get('category')
    const featured = searchParams.get('featured')
    const status = searchParams.get('status')
    const search = searchParams.get('search')

    // Build where clause
    const where: any = {
      published: { equals: true }
    }

    if (category) where.category = { equals: category }
    if (featured === 'true') where.featured = { equals: true }
    if (status) where.status = { equals: status }

    if (search) {
      where.or = [
        { title: { contains: search } },
        { summary: { contains: search } }
      ]
    }

    // Fetch projects directly from PayloadCMS
    const result = await payload.find({
      collection: 'projects',
      where,
      page,
      limit,
      sort: featured === 'true' ? ['-featured', '-createdAt'] : ['-createdAt'],
      populate: {
        'image.image': true,
        'location.counties': true,
      },
    })

    return NextResponse.json({
      success: true,
      projects: result.docs,
      pagination: {
        totalDocs: result.totalDocs,
        totalPages: result.totalPages,
        page: result.page,
        limit: result.limit,
        hasNextPage: result.hasNextPage,
        hasPrevPage: result.hasPrevPage,
      }
    })
  } catch (error) {
    console.error('Projects API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })

    // Parse request body with better error handling for both JSON and FormData
    let body
    let rawBody = ''

    try {
      const contentType = request.headers.get('content-type') || ''
      console.log('Content-Type:', contentType)

      if (contentType.includes('multipart/form-data')) {
        // Handle multipart form data (from Payload CMS admin)
        const formData = await request.formData()
        const payloadData = formData.get('_payload')

        if (payloadData && typeof payloadData === 'string') {
          console.log('Parsing _payload from form data')
          body = JSON.parse(payloadData)
        } else {
          console.error('No _payload field found in form data')
          return NextResponse.json(
            {
              error: 'Invalid form data',
              message: 'Missing _payload field in form data',
            },
            { status: 400 },
          )
        }
      } else {
        // Handle regular JSON requests
        rawBody = await request.text()
        console.log('Raw request body length:', rawBody.length)
        console.log('Raw request body preview:', rawBody.substring(0, 200))

        if (!rawBody || rawBody.trim() === '') {
          console.error('Empty request body received')
          return NextResponse.json(
            {
              error: 'Invalid JSON',
              message: 'Request body is empty',
            },
            { status: 400 },
          )
        }

        body = JSON.parse(rawBody)
      }

      console.log('Parsed body keys:', Object.keys(body))
    } catch (parseError) {
      console.error('JSON parsing error:', parseError)
      console.error('Raw body that failed to parse:', rawBody)
      return NextResponse.json(
        {
          error: 'Invalid JSON',
          message: 'Request body must be valid JSON or form data',
          details: parseError instanceof Error ? parseError.message : 'Unknown parsing error',
        },
        { status: 400 },
      )
    }

    // Sanitize array fields that come as 0 from form data
    const arrayFields = [
      'tags',
      'gallery',
      'impact.metrics',
      'budget.fundingSources',
      'resources.links',
      'resources.documents',
      'timeline.milestones',
      'team.keyPersonnel',
      'team.implementingPartners',
    ]

    arrayFields.forEach((fieldPath) => {
      const keys = fieldPath.split('.')
      let current = body

      // Navigate to the parent object
      for (let i = 0; i < keys.length - 1; i++) {
        if (current[keys[i]] === undefined) {
          current[keys[i]] = {}
        }
        current = current[keys[i]]
      }

      // Set the final field to empty array if it's 0 or not an array
      const finalKey = keys[keys.length - 1]
      if (
        current[finalKey] === 0 ||
        current[finalKey] === '0' ||
        !Array.isArray(current[finalKey])
      ) {
        current[finalKey] = []
      }
    })

    // Validate required fields (image is optional for testing)
    const requiredFields = ['title', 'summary', 'category', 'pillar']
    const missingFields = requiredFields.filter((field) => !body[field])

    // Check for timeline.startDate specifically since timeline is a group with required startDate
    if (!body.timeline || !body.timeline.startDate) {
      missingFields.push('timeline.startDate')
    }

    if (missingFields.length > 0) {
      return NextResponse.json(
        {
          error: 'Validation error',
          message: `Missing required fields: ${missingFields.join(', ')}`,
        },
        { status: 400 },
      )
    }

    // Validate enum values
    const validCategories = [
      'knowledge-preservation',
      'community-empowerment',
      'capacity-building',
      'research-development',
      'policy-advocacy',
      'market-development',
      'technology-innovation',
    ]

    const validPillars = [
      'indigenous-knowledge',
      'community-innovation',
      'capacity-building',
      'market-development',
      'policy-framework',
    ]

    const validStatuses = ['planning', 'active', 'completed', 'on-hold', 'cancelled']

    if (!validCategories.includes(body.category)) {
      return NextResponse.json(
        {
          error: 'Validation error',
          message: `Invalid category. Must be one of: ${validCategories.join(', ')}`,
        },
        { status: 400 },
      )
    }

    if (!validPillars.includes(body.pillar)) {
      return NextResponse.json(
        {
          error: 'Validation error',
          message: `Invalid pillar. Must be one of: ${validPillars.join(', ')}`,
        },
        { status: 400 },
      )
    }

    if (body.status && !validStatuses.includes(body.status)) {
      return NextResponse.json(
        {
          error: 'Validation error',
          message: `Invalid status. Must be one of: ${validStatuses.join(', ')}`,
        },
        { status: 400 },
      )
    }

    // Prepare data for creation
    const projectData = {
      ...body,
      // Set default values for optional fields
      status: body.status || 'active',
      featured: body.featured || false,
      published: body.published !== false, // Default to true unless explicitly false
    }

    // Remove image field if it's not a valid ObjectId (for testing purposes)
    if (projectData.image && typeof projectData.image === 'string') {
      // Simple check for ObjectId format (24 hex characters)
      if (!/^[0-9a-fA-F]{24}$/.test(projectData.image)) {
        console.log('Invalid image ID provided, removing from data:', projectData.image)
        delete projectData.image
      }
    }

    // Create new project
    const result = await payload.create({
      collection: 'projects',
      data: projectData,
    })

    return NextResponse.json(
      {
        success: true,
        message: 'Project created successfully',
        project: result,
      },
      { status: 201 },
    )
  } catch (error) {
    console.error('Projects POST error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to create project',
      },
      { status: 500 },
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const payload = await getPayload({ config })

    // Handle both JSON and form data
    let body
    const contentType = request.headers.get('content-type') || ''

    if (contentType.includes('multipart/form-data')) {
      const formData = await request.formData()
      const payloadData = formData.get('_payload')

      if (payloadData && typeof payloadData === 'string') {
        body = JSON.parse(payloadData)
      } else {
        return NextResponse.json(
          {
            error: 'Invalid form data',
            message: 'Missing _payload field in form data',
          },
          { status: 400 },
        )
      }
    } else {
      body = await request.json()
    }

    const { id, ...updateData } = body

    if (!id) {
      return NextResponse.json(
        {
          error: 'Validation error',
          message: 'Project ID is required for updates',
        },
        { status: 400 },
      )
    }

    // Update project
    const result = await payload.update({
      collection: 'projects',
      id,
      data: updateData,
    })

    return NextResponse.json({
      success: true,
      message: 'Project updated successfully',
      project: result,
    })
  } catch (error) {
    console.error('Projects PUT error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to update project',
      },
      { status: 500 },
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const searchParams = request.nextUrl.searchParams
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        {
          error: 'Validation error',
          message: 'Project ID is required for deletion',
        },
        { status: 400 },
      )
    }

    // Delete project
    await payload.delete({
      collection: 'projects',
      id,
    })

    return NextResponse.json({
      success: true,
      message: 'Project deleted successfully',
    })
  } catch (error) {
    console.error('Projects DELETE error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to delete project',
      },
      { status: 500 },
    )
  }
}
