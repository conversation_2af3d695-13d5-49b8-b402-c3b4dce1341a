import type { Payload } from 'payload'

export default async function testSeed(payload: Payload) {
  payload.logger.info('🧪 Testing simple seed operation...')
  
  try {
    // Test creating a simple county entry
    const testCounty = await payload.create({
      collection: 'counties',
      data: {
        name: 'Test County',
        code: 'TEST-001',
        coordinates: {
          latitude: -1.0,
          longitude: 36.0,
        },
        description: 'Test county for seeding verification',
        isActive: true,
      },
    })
    
    payload.logger.info('✅ Successfully created test county:', testCounty.id)
    
    // Test creating a simple project
    const testProject = await payload.create({
      collection: 'projects',
      data: {
        title: 'Test Project',
        description: {
          root: {
            children: [
              {
                children: [
                  {
                    detail: 0,
                    format: 0,
                    mode: 'normal' as const,
                    style: '',
                    text: 'This is a test project to verify seeding functionality.',
                    type: 'text' as const,
                    version: 1,
                  },
                ],
                direction: 'ltr' as const,
                format: '' as const,
                indent: 0,
                type: 'paragraph' as const,
                version: 1,
              },
            ],
            direction: 'ltr' as const,
            format: '' as const,
            indent: 0,
            type: 'root' as const,
            version: 1,
          },
        },
        summary: 'Test project for seeding verification',
        category: 'research-development' as const,
        pillar: 'capacity-building' as const,
        status: 'active' as const,
        location: {
          counties: [testCounty.id],
          specificLocation: 'Test Location',
          coordinates: {
            latitude: -1.0,
            longitude: 36.0,
          },
        },
        timeline: {
          startDate: '2024-01-01',
          endDate: '2024-12-31',
          duration: '1 year',
        },
        budget: {
          totalBudget: 1000000,
          currency: 'KES',
          fundingSources: [
            {
              source: 'Test Funding',
              amount: 1000000,
              percentage: 100,
            },
          ],
        },
        impact: {
          beneficiaries: 100,
          communities: 5,
          jobsCreated: 10,
        },
        team: {
          projectManager: 'Test Manager',
        },
        featured: false,
        published: true,
        tags: [
          { tag: 'test' },
        ],
        slug: 'test-project',
      },
    })
    
    payload.logger.info('✅ Successfully created test project:', testProject.id)
    
    // Check if data exists
    const counties = await payload.find({
      collection: 'counties',
      limit: 10,
    })
    
    const projects = await payload.find({
      collection: 'projects',
      limit: 10,
    })
    
    payload.logger.info(`📊 Found ${counties.totalDocs} counties and ${projects.totalDocs} projects`)
    
    payload.logger.info('🎉 Simple seed test completed successfully!')
    
  } catch (error) {
    payload.logger.error('❌ Error in simple seed test:', error)
    throw error
  }
}
