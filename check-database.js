// Simple script to check database contents
import { getPayload } from 'payload'
import config from './src/payload.config.ts'

async function checkDatabase() {
  try {
    console.log('🔍 Checking database contents...')
    
    const payload = await getPayload({ config })
    
    // Check each collection
    const collections = ['counties', 'projects', 'success-stories', 'news', 'events', 'resources']
    
    for (const collection of collections) {
      try {
        const result = await payload.count({
          collection: collection,
        })
        console.log(`📊 ${collection}: ${result.totalDocs} documents`)
      } catch (error) {
        console.log(`❌ ${collection}: Error - ${error.message}`)
      }
    }
    
    console.log('✅ Database check completed')
    process.exit(0)
    
  } catch (error) {
    console.error('❌ Database check failed:', error.message)
    process.exit(1)
  }
}

checkDatabase()
