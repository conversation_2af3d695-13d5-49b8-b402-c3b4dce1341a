import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

export async function GET() {
  try {
    const payload = await getPayload({ config })
    
    // Test basic connection by getting collections info
    const collections = payload.config.collections.map(col => col.slug)
    
    // Try to count documents in each collection
    const counts = {}
    for (const collection of collections) {
      try {
        const result = await payload.count({
          collection: collection as any,
        })
        counts[collection] = result.totalDocs
      } catch (err) {
        counts[collection] = `Error: ${err instanceof Error ? err.message : 'Unknown'}`
      }
    }
    
    return NextResponse.json({
      success: true,
      message: 'CMS connection successful',
      collections,
      documentCounts: counts,
    })

  } catch (error) {
    console.error('CMS test error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to connect to CMS',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
