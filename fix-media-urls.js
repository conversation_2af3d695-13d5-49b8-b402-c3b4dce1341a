#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to fix media URLs for database-stored images
 * Run with: node fix-media-urls.js
 */

const { getPayload } = require('payload')
const config = require('./dist/payload.config.js').default

async function fixMediaUrls() {
  console.log('🔧 Fixing media URLs for database-stored images...\n')

  try {
    const payload = await getPayload({ config })

    // Find all media items with database storage
    const mediaItems = await payload.find({
      collection: 'media',
      where: {
        storageType: {
          equals: 'database'
        }
      },
      limit: 100,
    })

    console.log(`Found ${mediaItems.docs.length} database-stored media items`)

    let fixedCount = 0
    let errorCount = 0

    for (const media of mediaItems.docs) {
      try {
        if (media.base64Data && (!media.url || media.url.startsWith('data:'))) {
          // Update the URL to point to the database endpoint
          await payload.update({
            collection: 'media',
            id: media.id,
            data: {
              url: `/api/media/database/${media.id}`,
            },
          })

          console.log(`✅ Fixed URL for ${media.filename} (${media.id})`)
          fixedCount++
        } else if (!media.base64Data) {
          console.log(`⚠️  ${media.filename} (${media.id}) has no base64 data`)
        } else {
          console.log(`ℹ️  ${media.filename} (${media.id}) already has correct URL`)
        }
      } catch (error) {
        console.log(`❌ Failed to fix ${media.filename} (${media.id}): ${error.message}`)
        errorCount++
      }
    }

    console.log(`\n🏁 Completed! Fixed ${fixedCount} media URLs, ${errorCount} errors`)

    // Test a few media endpoints
    console.log('\n🧪 Testing media endpoints...')
    
    const testMedia = mediaItems.docs.slice(0, 3)
    for (const media of testMedia) {
      try {
        const testResult = await payload.findByID({
          collection: 'media',
          id: media.id,
        })
        
        if (testResult && testResult.base64Data) {
          console.log(`✅ ${media.filename} - Database data exists (${testResult.base64Data.length} chars)`)
        } else {
          console.log(`❌ ${media.filename} - No database data found`)
        }
      } catch (error) {
        console.log(`❌ ${media.filename} - Test failed: ${error.message}`)
      }
    }

    process.exit(0)
  } catch (error) {
    console.error('❌ Error fixing media URLs:', error)
    process.exit(1)
  }
}

fixMediaUrls()
