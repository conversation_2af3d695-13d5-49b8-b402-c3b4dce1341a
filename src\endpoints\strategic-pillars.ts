import type { PayloadRequest } from 'payload'

// Strategic Pillars Handler
export const strategicPillarsHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req

    // Parse query parameters
    const {
      published = 'true',
      limit = '20',
      page = '1',
      sort = 'order',
    } = req.query as Record<string, string>

    // Safely parse integers with fallbacks
    const parsedPage = parseInt(page) || 1
    const parsedLimit = Math.min(parseInt(limit) || 20, 100) // Cap at 100

    // Build where clause
    const where: any = {}
    
    if (published === 'true') {
      where.published = { equals: true }
    }

    // Fetch strategic pillars from PayloadCMS
    const result = await payload.find({
      collection: 'strategic-pillars',
      where,
      page: parsedPage,
      limit: parsedLimit,
      sort: [sort],
      populate: {
        relatedProjects: true,
      },
    })

    return res.status(200).json({
      success: true,
      pillars: result.docs,
      pagination: {
        totalDocs: result.totalDocs,
        totalPages: result.totalPages,
        page: result.page,
        limit: result.limit,
        hasNextPage: result.hasNextPage,
        hasPrevPage: result.hasPrevPage,
      }
    })
  } catch (error) {
    console.error('Strategic Pillars API error:', error)
    return res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

// Strategic Pillar by ID Handler
export const strategicPillarByIdHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req
    const { id } = req.routeParams || {}

    if (!id) {
      return res.status(400).json({
        success: false,
        error: 'Pillar ID is required',
      })
    }

    let result

    // First try to find by ID (if it's a valid ObjectId format)
    if (id.match(/^[0-9a-fA-F]{24}$/)) {
      try {
        result = await payload.findByID({
          collection: 'strategic-pillars',
          id,
          populate: {
            relatedProjects: true,
          },
        })
      } catch (error) {
        // If ID lookup fails, try pillarId lookup
        result = null
      }
    }

    // If no result from ID lookup or ID is not ObjectId format, try pillarId lookup
    if (!result) {
      const pillarIdResult = await payload.find({
        collection: 'strategic-pillars',
        where: {
          pillarId: {
            equals: id,
          },
        },
        limit: 1,
        populate: {
          relatedProjects: true,
        },
      })

      if (pillarIdResult.docs.length > 0) {
        result = pillarIdResult.docs[0]
      }
    }

    if (!result) {
      return res.status(404).json({
        success: false,
        error: 'Strategic pillar not found',
      })
    }

    return res.status(200).json({
      success: true,
      pillar: result,
    })
  } catch (error) {
    console.error('Strategic Pillar by ID API error:', error)
    return res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

// Page Content Handler
export const pageContentHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req

    // Parse query parameters
    const {
      pageSlug,
      sectionKey,
      published = 'true',
      limit = '50',
      page = '1',
      sort = 'displayOrder',
    } = req.query as Record<string, string>

    // Safely parse integers with fallbacks
    const parsedPage = parseInt(page) || 1
    const parsedLimit = Math.min(parseInt(limit) || 50, 100) // Cap at 100

    // Build where clause
    const where: any = {}
    
    if (published === 'true') {
      where.published = { equals: true }
    }
    
    if (pageSlug) {
      where.pageSlug = { equals: pageSlug }
    }
    
    if (sectionKey) {
      where.sectionKey = { equals: sectionKey }
    }

    // Fetch page content from PayloadCMS
    const result = await payload.find({
      collection: 'page-content',
      where,
      page: parsedPage,
      limit: parsedLimit,
      sort: [sort],
      populate: {
        backgroundImage: true,
        featuredImage: true,
      },
    })

    return res.status(200).json({
      success: true,
      content: result.docs,
      pagination: {
        totalDocs: result.totalDocs,
        totalPages: result.totalPages,
        page: result.page,
        limit: result.limit,
        hasNextPage: result.hasNextPage,
        hasPrevPage: result.hasPrevPage,
      }
    })
  } catch (error) {
    console.error('Page Content API error:', error)
    return res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

// Statistics Handler
export const statisticsHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req

    // Parse query parameters
    const {
      category,
      featured,
      published = 'true',
      limit = '50',
      page = '1',
      sort = 'displayOrder',
    } = req.query as Record<string, string>

    // Safely parse integers with fallbacks
    const parsedPage = parseInt(page) || 1
    const parsedLimit = Math.min(parseInt(limit) || 50, 100) // Cap at 100

    // Build where clause
    const where: any = {}
    
    if (published === 'true') {
      where.published = { equals: true }
    }
    
    if (category) {
      where.category = { equals: category }
    }
    
    if (featured === 'true') {
      where.featured = { equals: true }
    }

    // Fetch statistics from PayloadCMS
    const result = await payload.find({
      collection: 'statistics',
      where,
      page: parsedPage,
      limit: parsedLimit,
      sort: [sort],
    })

    return res.status(200).json({
      success: true,
      statistics: result.docs,
      pagination: {
        totalDocs: result.totalDocs,
        totalPages: result.totalPages,
        page: result.page,
        limit: result.limit,
        hasNextPage: result.hasNextPage,
        hasPrevPage: result.hasPrevPage,
      }
    })
  } catch (error) {
    console.error('Statistics API error:', error)
    return res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}
