'use client'

import React, { useState, useEffect } from 'react'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import {
  NPICard,
  NPICardHeader,
  NPICardTitle,
  NPICardContent,
  NPICardFooter,
} from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import Link from 'next/link'
import Image from 'next/image'
import { Calendar, User, Tag, ArrowRight, Filter, Search, Eye, Play, Download } from 'lucide-react'
import { NPINewsletter } from '@/components/ui/npi-newsletter'

interface NewsArticle {
  id: string
  title: string
  excerpt: string
  content: string
  image: string
  author: string
  date: string
  category: string
  type: 'article' | 'video' | 'report' | 'document'
  tags: string[]
  readTime: string
  views: number
  featured?: boolean
}

interface NPINewsListingProps {
  title?: string
  description?: string
  articles?: NewsArticle[]
}

export const NPINewsListingBlock: React.FC<NPINewsListingProps> = ({
  title = 'Latest News & Updates',
  description = "Stay informed about the latest developments, achievements, and insights from Kenya's natural products sector.",
}) => {
  const [articles, setArticles] = useState<NewsArticle[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchNews = async () => {
      try {
        setLoading(true)
        setError(null)

        const response = await fetch('/api/news?limit=12&sort=-publishDate')

        if (!response.ok) {
          throw new Error(`Failed to fetch news: ${response.status}`)
        }

        const data = await response.json()

        if (data.success && data.news) {
          // Transform database data to match component interface
          const transformedArticles = data.news.map((article: any) => ({
            id: article.id,
            title: article.title,
            excerpt: article.summary || article.subtitle || 'Read more about this news article.',
            content: article.content || 'Full article content...',
            image: article.featuredImage?.image?.url || article.featuredImage?.url || '/assets/product 4.jpg',
            author: article.author?.name || article.author || 'NPI Team',
            date: article.publishDate || article.createdAt,
            category: article.category || 'News',
            type: article.type || 'article',
            tags: article.tags?.map((tag: any) => tag.tag || tag) || [],
            featured: article.featured || false,
            urgent: article.urgent || false,
            slug: article.slug || article.id,
          }))
          setArticles(transformedArticles)
        } else {
          throw new Error('Invalid response format')
        }
      } catch (err) {
        console.error('Error fetching news:', err)
        setError(err instanceof Error ? err.message : 'Failed to load news')
        // Fallback to empty array
        setArticles([])
      } finally {
        setLoading(false)
      }
    }

    fetchNews()
  }, [])

  // Fallback data for when database is empty
  const fallbackArticles = [
    {
      id: 'npi-launches-documentation-platform',
      title: 'NPI Launches Revolutionary Knowledge Documentation Platform',
      excerpt:
        "A new digital platform now provides enhanced access to Kenya's traditional knowledge heritage, supporting researchers and communities nationwide.",
      content: 'Full article content here...',
      image: '/assets/product 4.jpg',
      author: 'Dr. Sarah Kiprotich',
      date: '2024-01-15',
      category: 'Platform Launch',
      type: 'article',
      tags: ['documentation', 'platform', 'traditional knowledge', 'technology'],
      readTime: '5 min read',
      views: 2450,
      featured: true,
    },
    {
      id: 'maasai-ip-protection-success',
      title: 'Historic IP Protection Success for Maasai Traditional Medicine',
      excerpt:
        'Maasai community achieves groundbreaking intellectual property protection for traditional healing formulations, setting precedent for indigenous rights.',
      content: 'Full article content here...',
      image: '/assets/hero image.jpg',
      author: 'James Sankale',
      date: '2024-01-10',
      category: 'IP Protection',
      type: 'video',
      tags: ['intellectual property', 'Maasai', 'traditional medicine', 'legal'],
      readTime: '4 min read',
      views: 1890,
      featured: false,
    },
    {
      id: 'aloe-cooperative-national-award',
      title: 'Baringo Aloe Cooperative Wins National Innovation Award',
      excerpt:
        "Women's aloe vera cooperative in Baringo County receives prestigious national recognition for outstanding community-led innovation.",
      content: 'Full article content here...',
      image: '/assets/product 5.jpg',
      author: 'Mary Chepkemoi',
      date: '2024-01-05',
      category: 'Awards',
      type: 'report',
      tags: ['awards', 'innovation', 'women empowerment', 'aloe vera'],
      readTime: '3 min read',
      views: 1650,
      featured: false,
    },
    {
      id: 'international-investment-forum',
      title: 'International Investment Forum Attracts $50M in Commitments',
      excerpt:
        "NPI's first International Natural Products Investment Forum successfully attracts significant funding commitments for community-based projects.",
      content: 'Full article content here...',
      image: '/assets/background.jpg',
      author: 'Dr. Peter Murithi',
      date: '2023-12-20',
      category: 'Investment',
      type: 'document',
      tags: ['investment', 'funding', 'international', 'forum'],
      readTime: '6 min read',
      views: 2100,
      featured: true,
    },
    {
      id: 'youth-moringa-enterprise',
      title: 'Turkana Youth Transform Moringa into Thriving Enterprise',
      excerpt:
        'Young entrepreneurs in Turkana County develop sustainable moringa processing facility, creating jobs and nutritious products.',
      content: 'Full article content here...',
      image: '/assets/product 1.jpg',
      author: 'John Ekale',
      date: '2023-12-15',
      category: 'Youth Empowerment',
      type: 'article',
      tags: ['youth', 'entrepreneurship', 'moringa', 'Turkana'],
      readTime: '4 min read',
      views: 1420,
      featured: false,
    },
    {
      id: 'research-collaboration-announcement',
      title: 'New Research Collaboration with International Universities',
      excerpt:
        'NPI announces groundbreaking research partnerships with leading universities to advance natural products science.',
      content: 'Full article content here...',
      image: '/assets/product 6.jpg',
      author: 'Prof. Grace Mutindi',
      date: '2023-12-10',
      category: 'Research',
      type: 'video',
      tags: ['research', 'collaboration', 'universities', 'science'],
      readTime: '5 min read',
      views: 1780,
      featured: false,
    },
    {
      id: 'environmental-conservation-initiative',
      title: 'New Environmental Conservation Initiative Launched',
      excerpt:
        'Comprehensive program to protect biodiversity while supporting sustainable natural products development across Kenya.',
      content: 'Full article content here...',
      image: '/assets/product 1.jpg',
      author: 'Dr. Samuel Lekorere',
      date: '2023-12-05',
      category: 'Conservation',
      tags: ['conservation', 'biodiversity', 'environment', 'sustainability'],
      readTime: '4 min read',
      views: 1320,
      featured: false,
    },
    {
      id: 'community-training-program',
      title: 'Comprehensive Community Training Program Reaches 1000 Participants',
      excerpt:
        "Milestone achievement as NPI's capacity building programs successfully train over 1000 community members across Kenya.",
      content: 'Full article content here...',
      image: '/assets/product 1.jpg',
      author: 'Catherine Wanjiku',
      date: '2023-11-28',
      category: 'Training',
      tags: ['training', 'capacity building', 'community', 'milestone'],
      readTime: '3 min read',
      views: 980,
      featured: false,
    },
  ]

  // Use database data if available, otherwise use fallback
  const displayArticles = articles.length > 0 ? articles : fallbackArticles
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('All Categories')

  const categories = ['All Categories', ...Array.from(new Set(displayArticles.map((a) => a.category)))]

  const filteredArticles = displayArticles.filter((article) => {
    const matchesSearch =
      article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      article.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
      article.tags.some((tag) => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    const matchesCategory =
      selectedCategory === 'All Categories' || article.category === selectedCategory

    return matchesSearch && matchesCategory
  })

  const featuredArticles = filteredArticles.filter((a) => a.featured)
  const regularArticles = filteredArticles.filter((a) => !a.featured)

  return (
    <NPISection className="bg-[#E5E1DC]/30">
      <NPISectionHeader>
        <NPISectionTitle className="text-[#34170D]">{title}</NPISectionTitle>
        <NPISectionDescription className="text-[#46372A]">{description}</NPISectionDescription>
      </NPISectionHeader>

      {loading && (
        <div className="flex justify-center items-center py-12">
          <div className="text-[#34170D]">Loading news articles...</div>
        </div>
      )}

      {error && (
        <div className="flex justify-center items-center py-12">
          <div className="text-red-600">Error: {error}</div>
        </div>
      )}

      {!loading && !error && (
        <>
          {/* Search and Filters */}
      <NPICard className="mb-8 border-l-4 border-[#4C6444]">
        <NPICardContent className="p-6">
          <div className="flex gap-4 mb-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
              <input
                type="text"
                placeholder="Search news articles..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent font-npi"
              />
            </div>
          </div>

          <div className="flex items-center gap-4 mb-4">
            <Filter className="w-5 h-5 text-muted-foreground" />
            <span className="font-medium font-npi">Filter by category:</span>
          </div>
          <div className="grid md:grid-cols-1 gap-4">
            <div>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full p-2 border border-border rounded focus:outline-none focus:ring-2 focus:ring-primary font-npi"
              >
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </NPICardContent>
      </NPICard>

      {/* Featured Articles */}
      {featuredArticles.length > 0 && (
        <div className="mb-12">
          <h3 className="text-2xl font-bold mb-6 font-npi text-black">Featured Stories</h3>
          <div className="grid lg:grid-cols-4 gap-6">
            {featuredArticles.map((article, index) => (
              <NPICard
                key={article.id}
                className={`overflow-hidden hover:shadow-xl transition-all duration-300 aspect-square flex flex-col border-2 relative ${
                  index % 2 === 0
                    ? 'bg-white border-[#8A3E25] hover:border-[#25718A]'
                    : 'bg-[#EFE3BA] border-[#725242] hover:border-[#8A3E25]'
                }`}
              >
                {/* Background Image */}
                <Image src={article.image} alt={article.title} fill className="object-cover" />
                <div className="absolute inset-0 bg-black/40" />

                {/* Top badges */}
                <div className="absolute top-3 left-3">
                  <span className="bg-[#8A3E25] text-white px-2 py-1 text-xs font-medium">
                    Featured
                  </span>
                </div>
                <div className="absolute top-3 right-3">
                  <span className="bg-[#725242] text-white px-2 py-1 text-xs font-npi">
                    {article.category}
                  </span>
                </div>

                {/* Bottom content overlay */}
                <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/80 to-transparent">
                  <h3 className="text-white font-bold text-sm line-clamp-2 mb-2">{article.title}</h3>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 text-xs text-white/80">
                      <Calendar className="w-3 h-3" />
                      <span>{new Date(article.date).toLocaleDateString()}</span>
                    </div>
                    <NPIButton
                      asChild
                      size="sm"
                      className="bg-[#25718A] hover:bg-[#8A3E25] text-white px-3 py-1 text-xs"
                    >
                      <Link href={`/news/${article.id}`}>
                        {article.type === 'video' ? (
                          <>Watch <Play className="w-3 h-3 ml-1" /></>
                        ) : article.type === 'report' || article.type === 'document' ? (
                          <>Download <Download className="w-3 h-3 ml-1" /></>
                        ) : (
                          <>Read More <ArrowRight className="w-3 h-3 ml-1" /></>
                        )}
                      </Link>
                    </NPIButton>
                  </div>
                </div>
              </NPICard>
            ))}
          </div>
        </div>
      )}

      {/* Regular Articles */}
      <div className="grid lg:grid-cols-4 gap-6">
        {regularArticles.map((article, index) => {
          // Cycle through balanced color palette for category badges
          const colorClasses = [
            'bg-[#8A3E25] text-white', // Reddish brown with white text
            'bg-[#725242] text-white', // Brown with white text
            'bg-[#25718A] text-white', // Blue accent with white text
            'bg-[#EFE3BA] text-black', // Cream with black text
          ]
          const colorClass = colorClasses[index % colorClasses.length]

          return (
            <NPICard
              key={article.id}
              className={`hover:shadow-lg transition-all duration-300 aspect-square flex flex-col border-2 relative ${
                index % 2 === 0
                  ? 'bg-[#EFE3BA] border-[#725242] hover:border-[#8A3E25]'
                  : 'bg-white border-[#8A3E25] hover:border-[#25718A]'
              }`}
            >
              {/* Background Image */}
              <Image src={article.image} alt={article.title} fill className="object-cover" />
              <div className="absolute inset-0 bg-black/40" />

              {/* Top category badge */}
              <div className="absolute top-3 right-3">
                <span className={`${colorClass} px-2 py-1 text-xs font-npi font-medium`}>
                  {article.category}
                </span>
              </div>

              {/* Bottom content overlay */}
              <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/80 to-transparent">
                <h3 className="text-white font-bold text-sm line-clamp-2 mb-2">{article.title}</h3>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 text-xs text-white/80">
                    <Calendar className="w-3 h-3" />
                    <span>{new Date(article.date).toLocaleDateString()}</span>
                  </div>
                  <NPIButton
                    asChild
                    size="sm"
                    className="bg-[#25718A] hover:bg-[#8A3E25] text-white px-3 py-1 text-xs"
                  >
                    <Link href={`/news/${article.id}`}>
                      {article.type === 'video' ? (
                        <>Watch <Play className="w-3 h-3 ml-1" /></>
                      ) : article.type === 'report' || article.type === 'document' ? (
                        <>Download <Download className="w-3 h-3 ml-1" /></>
                      ) : (
                        <>Read More <ArrowRight className="w-3 h-3 ml-1" /></>
                      )}
                    </Link>
                  </NPIButton>
                </div>
              </div>
            </NPICard>
          )
        })}
      </div>

      {/* Newsletter Signup */}
      <div className="mt-12">
        <NPINewsletter
          title="Stay Informed"
          description="Subscribe to our newsletter to receive the latest news, updates, and insights from Kenya's natural products sector directly in your inbox."
        />
      </div>

          {/* Results Summary */}
          <div className="text-center mt-8">
            <p className="text-[#725242] font-npi">
              Showing {filteredArticles.length} of {displayArticles.length} articles
            </p>
          </div>
        </>
      )}
    </NPISection>
  )
}
