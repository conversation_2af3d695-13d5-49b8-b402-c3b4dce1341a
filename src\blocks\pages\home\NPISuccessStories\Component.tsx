'use client'

import React, { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import {
  N<PERSON><PERSON>ard,
  NPICardHeader,
  NPICardTitle,
  NPICardContent,
  NPICardFooter,
} from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import Image from 'next/image'
import Link from 'next/link'
import { Quote, MapPin, TrendingUp, Loader2 } from 'lucide-react'

interface CMSSuccessStory {
  id: string
  title: string
  summary: string
  image?: {
    image?: {
      url?: string
      alt?: string
    }
    alt?: string
    caption?: string
  }
  location?: {
    specificLocation?: string
    county?: { name: string }
  }
  category: string
  impact?: {
    beneficiaries?: number
    jobsCreated?: number
    incomeIncrease?: {
      percentage?: number
    }
    metrics?: Array<{
      metric: string
      value: string
      unit: string
      description?: string
    }>
  }
  testimonials?: Array<{
    quote: string
    author: string
    role: string
    organization?: string
  }>
  slug: string
}

interface NPISuccessStoriesProps {
  title?: string
  description?: string
  limit?: number
}

export const NPISuccessStoriesBlock: React.FC<NPISuccessStoriesProps> = ({
  title = 'Success Stories',
  description = 'Real impact, real change. Discover how our initiatives are transforming lives and communities across Kenya.',
  limit = 3,
}) => {
  const [stories, setStories] = useState<CMSSuccessStory[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchFeaturedStories = async () => {
      try {
        setLoading(true)
        setError(null)

        const response = await fetch(`/api/success-stories?featured=true&limit=${limit}`)

        if (!response.ok) {
          throw new Error(`Failed to fetch success stories: ${response.status}`)
        }

        const data = await response.json()

        if (data.success && data.stories) {
          setStories(data.stories)
        } else {
          throw new Error('Invalid response format')
        }
      } catch (err) {
        console.error('Error fetching featured success stories:', err)
        setError(err instanceof Error ? err.message : 'Failed to load success stories')
      } finally {
        setLoading(false)
      }
    }

    fetchFeaturedStories()
  }, [limit])

  // Helper function to get image URL
  const getImageUrl = (story: CMSSuccessStory): string => {
    if (story.image?.image?.url) {
      return story.image.image.url
    }
    // Fallback to a default image
    return '/assets/product-placeholder.jpg'
  }

  // Helper function to get image alt text
  const getImageAlt = (story: CMSSuccessStory): string => {
    return story.image?.alt || story.image?.image?.alt || story.title
  }

  // Helper function to format location
  const getLocation = (story: CMSSuccessStory): string => {
    if (story.location?.specificLocation) {
      return story.location.specificLocation
    }
    if (story.location?.county?.name) {
      return story.location.county.name
    }
    return 'Kenya'
  }

  // Helper function to format category
  const formatCategory = (category: string): string => {
    return category.split('-').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')
  }

  // Helper function to format impact
  const getImpactSummary = (story: CMSSuccessStory): string => {
    const impacts = []

    if (story.impact?.jobsCreated) {
      impacts.push(`${story.impact.jobsCreated} jobs created`)
    }

    if (story.impact?.incomeIncrease?.percentage) {
      impacts.push(`${story.impact.incomeIncrease.percentage}% income increase`)
    }

    if (story.impact?.beneficiaries) {
      impacts.push(`${story.impact.beneficiaries} beneficiaries`)
    }

    if (story.impact?.metrics && story.impact.metrics.length > 0) {
      const firstMetric = story.impact.metrics[0]
      impacts.push(`${firstMetric.value} ${firstMetric.unit} ${firstMetric.metric.toLowerCase()}`)
    }

    return impacts.length > 0 ? impacts.slice(0, 2).join(', ') : 'Positive community impact'
  }

  // Helper function to get testimonial
  const getTestimonial = (story: CMSSuccessStory) => {
    return story.testimonials && story.testimonials.length > 0 ? story.testimonials[0] : null
  }
  return (
    <NPISection size="sm" className="bg-[#EFE3BA] relative overflow-hidden">
      <div className="relative z-10">
        <NPISectionHeader className="text-center mb-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="inline-flex items-center px-4 py-2 bg-[#8A3E25] text-white text-sm font-semibold mb-3"
          >
            <TrendingUp className="w-4 h-4 mr-3" />
            Success Stories
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <NPISectionTitle className="leading-[1.1] tracking-[-0.02em] mb-2 text-black font-bold text-2xl lg:text-3xl">
              {title}
            </NPISectionTitle>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <NPISectionDescription className="font-light leading-[1.6] text-[#725242] max-w-xl mx-auto text-sm lg:text-base">
              {description}
            </NPISectionDescription>
          </motion.div>
        </NPISectionHeader>

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-[#8A3E25]" />
            <span className="ml-2 text-[#725242]">Loading success stories...</span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="text-center py-12">
            <p className="text-red-600 mb-4">Failed to load success stories: {error}</p>
            <NPIButton
              onClick={() => window.location.reload()}
              variant="outline"
              className="border-[#8A3E25] text-[#8A3E25] hover:bg-[#8A3E25] hover:text-white"
            >
              Try Again
            </NPIButton>
          </div>
        )}

        {/* Stories Grid */}
        {!loading && !error && stories.length > 0 && (
          <div className="grid lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
            {stories.map((story, index) => (
            <motion.div
              key={story.id}
              initial={{ opacity: 0, y: 40, scale: 0.9 }}
              whileInView={{ opacity: 1, y: 0, scale: 1 }}
              viewport={{ once: true }}
              transition={{
                duration: 0.8,
                delay: index * 0.2,
                type: 'spring',
                stiffness: 80,
              }}
              whileHover={{ y: -8 }}
            >
              <NPICard
                className={`overflow-hidden shadow-lg border-2 transition-all duration-300 hover:shadow-xl group h-full hover:scale-[1.03] hover:-translate-y-1 ${
                  index % 3 === 0
                    ? 'bg-white border-[#8A3E25] hover:border-[#25718A]'
                    : index % 3 === 1
                      ? 'bg-[#EFE3BA] border-[#725242] hover:border-[#8A3E25]'
                      : 'bg-white border-[#25718A] hover:border-[#8A3E25]'
                }`}
              >
                {/* Square aspect ratio container */}
                <div className="relative w-full aspect-square">
                  <Image
                    src={getImageUrl(story)}
                    alt={getImageAlt(story)}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-black/40" />
                  <div className="absolute top-3 right-3">
                    <motion.div
                      initial={{ scale: 0 }}
                      whileInView={{ scale: 1 }}
                      transition={{ delay: 0.5, type: 'spring' }}
                      className="px-3 py-1 text-xs font-bold text-white bg-[#8A3E25] shadow-lg"
                    >
                      {formatCategory(story.category)}
                    </motion.div>
                  </div>
                  <div className="absolute bottom-3 left-3 right-3">
                    <div className="flex items-center text-white/90 text-xs font-light mb-1">
                      <MapPin className="w-3 h-3 mr-2" />
                      {getLocation(story)}
                    </div>
                    <div className="flex items-center text-white text-xs font-semibold">
                      <TrendingUp className="w-3 h-3 mr-2" />
                      {getImpactSummary(story)}
                    </div>
                  </div>
                </div>

                <NPICardHeader className="p-4">
                  <NPICardTitle
                    className={`text-lg font-bold leading-tight mb-2 transition-colors ${
                      index % 3 === 0 ? 'text-black' : index % 3 === 1 ? 'text-black' : 'text-black'
                    }`}
                  >
                    {story.title}
                  </NPICardTitle>
                </NPICardHeader>

                <NPICardContent className="px-4 pb-3">
                  <p
                    className={`leading-[1.5] mb-4 font-light text-sm ${
                      index % 3 === 0
                        ? 'text-[#725242]'
                        : index % 3 === 1
                          ? 'text-[#725242]'
                          : 'text-[#725242]'
                    }`}
                  >
                    {story.summary}
                  </p>

                  {getTestimonial(story) && (
                    <motion.blockquote
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.3 }}
                      className="border-l-2 border-[#25718A] pl-3 bg-[#EFE3BA]/50 p-3 relative"
                    >
                      <Quote className="absolute top-1 right-1 w-4 h-4 text-[#25718A]/50" />
                      <p
                        className={`mb-2 italic font-light leading-[1.4] text-xs ${
                          index % 3 === 0
                            ? 'text-[#725242]'
                            : index % 3 === 1
                              ? 'text-[#725242]'
                              : 'text-[#725242]'
                        }`}
                      >
                        &ldquo;{getTestimonial(story)?.quote}&rdquo;
                      </p>
                      <footer
                        className={`text-xs ${
                          index % 3 === 0
                            ? 'text-[#725242]'
                            : index % 3 === 1
                              ? 'text-[#725242]'
                              : 'text-[#725242]'
                        }`}
                      >
                        <strong className="font-semibold text-[#25718A]">
                          {getTestimonial(story)?.author}
                        </strong>
                        {getTestimonial(story)?.role && (
                          <span>, {getTestimonial(story)?.role}</span>
                        )}
                        {getTestimonial(story)?.organization && (
                          <span> - {getTestimonial(story)?.organization}</span>
                        )}
                      </footer>
                    </motion.blockquote>
                  )}
                </NPICardContent>

                <NPICardFooter className="p-4 pt-0">
                  <NPIButton
                    asChild
                    className="w-full bg-[#25718A] text-white hover:bg-[#8A3E25] font-medium transition-all duration-300"
                  >
                    <Link href={`/success-stories/${story.slug}`}>Read Full Story</Link>
                  </NPIButton>
                </NPICardFooter>
              </NPICard>
            </motion.div>
          ))}
          </div>
        )}

        {/* Empty State */}
        {!loading && !error && stories.length === 0 && (
          <div className="text-center py-12">
            <p className="text-[#725242] mb-4">No success stories available at the moment.</p>
            <Link href="/success-stories">
              <NPIButton
                variant="outline"
                className="border-[#8A3E25] text-[#8A3E25] hover:bg-[#8A3E25] hover:text-white"
              >
                View All Success Stories
              </NPIButton>
            </Link>
          </div>
        )}

        {/* View All Button */}
        {!loading && !error && stories.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="text-center mt-8"
          >
            <NPIButton
              asChild
              size="lg"
              className="bg-[#8A3E25] hover:bg-[#25718A] text-white font-bold px-8 py-3 shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <Link href="/success-stories">View All Success Stories</Link>
            </NPIButton>
          </motion.div>
        )}
      </div>
    </NPISection>
  )
}
