#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to seed the CMS database with comprehensive sample data
 * This will populate all collections with realistic sample content
 */

import { getPayload } from 'payload'
import config from '../src/payload.config.ts'

async function seedDatabase() {
  console.log('🌱 Starting CMS database seeding...')

  try {
    // Initialize Payload
    const payload = await getPayload({ config })
    console.log('✅ Payload initialized successfully')

    // Import the seeding function
    const { seedComprehensiveCMSData } = await import('../src/lib/seed/comprehensive-cms-data.ts')

    // Run the seeding
    await seedComprehensiveCMSData(payload)

    console.log('🎉 CMS database seeding completed successfully!')
    console.log('')
    console.log('📊 The following collections have been populated:')
    console.log('  • Counties (47 entries)')
    console.log('  • Media (sample images)')
    console.log('  • Projects (4 entries)')
    console.log('  • Success Stories (4 entries)')
    console.log('  • Events (8 entries)')
    console.log('  • News (4 entries)')
    console.log('  • Resources (4 entries)')
    console.log('  • Partners (4 entries)')
    console.log('  • Investment Opportunities (4 entries)')
    console.log('  • Media Gallery (4 entries)')
    console.log('')
    console.log('🌐 You can now:')
    console.log('  1. Visit the CMS admin at http://localhost:3000/admin')
    console.log('  2. View the populated content in the frontend')
    console.log('  3. Test the API endpoints with real data')

  } catch (error) {
    console.error('❌ Error seeding database:', error)
    process.exit(1)
  }

  process.exit(0)
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason)
  process.exit(1)
})

// Run the seeding
seedDatabase()
